<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ThemeSetting extends Model
{
    protected $fillable = [
        'top_nav_bg',
        'top_nav_text',
        'top_nav_icon_color',
        'main_nav_bg',
        'main_nav_text',
        'main_nav_hover',
        'instagram_url',
        'youtube_url',
        'facebook_url',
        'twitter_url',
        'is_active',
        'theme_name',
        'description'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // Scope untuk mendapatkan theme yang aktif
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Get current active theme
    public static function getCurrent()
    {
        return self::where('is_active', true)->first() ?? self::getDefault();
    }

    // Get default theme settings
    public static function getDefault()
    {
        return new self([
            'top_nav_bg' => '#1e40af',
            'top_nav_text' => '#ffffff',
            'top_nav_icon_color' => '#e5e7eb',
            'main_nav_bg' => '#2563eb',
            'main_nav_text' => '#ffffff',
            'main_nav_hover' => '#3b82f6',
            'instagram_url' => 'https://instagram.com',
            'youtube_url' => 'https://youtube.com',
            'facebook_url' => 'https://facebook.com',
            'twitter_url' => 'https://twitter.com',
            'is_active' => true,
            'theme_name' => 'Default',
            'description' => 'Default theme settings'
        ]);
    }

    // Generate CSS variables for theme
    public function toCssVariables()
    {
        return [
            '--top-nav-bg' => $this->top_nav_bg,
            '--top-nav-text' => $this->top_nav_text,
            '--top-nav-icon-color' => $this->top_nav_icon_color,
            '--main-nav-bg' => $this->main_nav_bg,
            '--main-nav-text' => $this->main_nav_text,
            '--main-nav-hover' => $this->main_nav_hover,
        ];
    }

    // Get social media URLs
    public function getSocialMediaUrls()
    {
        return [
            'instagram' => $this->instagram_url,
            'youtube' => $this->youtube_url,
            'facebook' => $this->facebook_url,
            'twitter' => $this->twitter_url,
        ];
    }
}
