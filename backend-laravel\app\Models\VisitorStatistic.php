<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class VisitorStatistic extends Model
{
    protected $fillable = [
        'date',
        'visitors_count',
        'page_views',
        'unique_visitors'
    ];

    protected $casts = [
        'date' => 'date',
        'visitors_count' => 'integer',
        'page_views' => 'integer',
        'unique_visitors' => 'integer'
    ];

    /**
     * Get statistics for the last N days
     */
    public static function getLastDays($days = 7)
    {
        return self::where('date', '>=', Carbon::now()->subDays($days))
            ->orderBy('date', 'asc')
            ->get();
    }

    /**
     * Get or create today's statistics
     */
    public static function getTodayStats()
    {
        return self::firstOrCreate(
            ['date' => Carbon::today()],
            [
                'visitors_count' => 0,
                'page_views' => 0,
                'unique_visitors' => 0
            ]
        );
    }

    /**
     * Increment visitor count for today
     */
    public static function incrementVisitor()
    {
        $today = self::getTodayStats();
        $today->increment('visitors_count');
        return $today;
    }

    /**
     * Increment page view count for today
     */
    public static function incrementPageView()
    {
        $today = self::getTodayStats();
        $today->increment('page_views');
        return $today;
    }
}
