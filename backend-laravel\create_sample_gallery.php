<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Gallery;
use App\Models\User;

echo "=== CREATING SAMPLE GALLERY DATA ===\n\n";

// Get existing gallery images
$galleryPath = public_path('images/gallery');
$imageFiles = glob($galleryPath . '/*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);

if (empty($imageFiles)) {
    echo "❌ No image files found in gallery directory\n";
    exit(1);
}

echo "📁 Found " . count($imageFiles) . " image files\n";

// Get or create admin user
$adminUser = User::where('email', '<EMAIL>')->first();
if (!$adminUser) {
    $adminUser = User::create([
        'name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role' => 'admin'
    ]);
    echo "👤 Created admin user\n";
}

// Categories for gallery
$categories = [
    'Kegiatan Rutin',
    'Kegiatan Khusus', 
    'Prestasi',
    'Fasilitas',
    'Ekstrakurikuler'
];

// Sample titles and descriptions
$sampleData = [
    [
        'title' => 'Kegiatan Pembelajaran di Kelas',
        'description' => 'Suasana pembelajaran yang kondusif dan interaktif di dalam kelas dengan fasilitas modern.',
        'category' => 'Kegiatan Rutin'
    ],
    [
        'title' => 'Upacara Bendera Hari Senin',
        'description' => 'Kegiatan rutin upacara bendera setiap hari Senin untuk menumbuhkan rasa nasionalisme.',
        'category' => 'Kegiatan Rutin'
    ],
    [
        'title' => 'Lomba Sains Tingkat Nasional',
        'description' => 'Siswa-siswi berprestasi meraih juara dalam kompetisi sains tingkat nasional.',
        'category' => 'Prestasi'
    ],
    [
        'title' => 'Fasilitas Laboratorium Komputer',
        'description' => 'Laboratorium komputer dengan perangkat terbaru untuk mendukung pembelajaran teknologi.',
        'category' => 'Fasilitas'
    ],
    [
        'title' => 'Kegiatan Ekstrakurikuler Pramuka',
        'description' => 'Kegiatan pramuka yang mengembangkan karakter dan kepemimpinan siswa.',
        'category' => 'Ekstrakurikuler'
    ],
    [
        'title' => 'Festival Seni dan Budaya',
        'description' => 'Acara tahunan festival seni dan budaya yang menampilkan kreativitas siswa.',
        'category' => 'Kegiatan Khusus'
    ],
    [
        'title' => 'Perpustakaan Sekolah',
        'description' => 'Fasilitas perpustakaan dengan koleksi buku yang lengkap dan ruang baca yang nyaman.',
        'category' => 'Fasilitas'
    ],
    [
        'title' => 'Tim Basket Juara Regional',
        'description' => 'Tim basket sekolah berhasil meraih juara 1 dalam kompetisi tingkat regional.',
        'category' => 'Prestasi'
    ]
];

$created = 0;
$errors = 0;

foreach ($imageFiles as $index => $imagePath) {
    if ($index >= count($sampleData)) {
        break; // Stop if we run out of sample data
    }
    
    $filename = basename($imagePath);
    $imageUrl = "http://localhost:8000/images/gallery/{$filename}";
    
    $data = $sampleData[$index];
    
    try {
        $gallery = Gallery::create([
            'title' => $data['title'],
            'description' => $data['description'],
            'category' => $data['category'],
            'image_url' => $imageUrl,
            'uploaded_by' => $adminUser->id,
            'is_active' => true,
            'featured' => $index < 3, // First 3 are featured
            'carousel_pinned' => $index < 2, // First 2 are pinned to carousel
            'sort_order' => $index
        ]);
        
        echo "✅ Created gallery item: {$data['title']} (ID: {$gallery->id})\n";
        echo "   Image: {$filename}\n";
        echo "   URL: {$imageUrl}\n";
        echo "   Category: {$data['category']}\n\n";
        
        $created++;
        
    } catch (Exception $e) {
        echo "❌ Failed to create gallery item: {$data['title']}\n";
        echo "   Error: {$e->getMessage()}\n\n";
        $errors++;
    }
}

echo "=== SUMMARY ===\n";
echo "✅ Successfully created: {$created} gallery items\n";
echo "❌ Errors: {$errors}\n";
echo "📊 Total gallery items in database: " . Gallery::count() . "\n";

// Show created items
echo "\n=== CREATED GALLERY ITEMS ===\n";
$galleries = Gallery::with('uploader:id,name')->get();
foreach ($galleries as $gallery) {
    echo "ID: {$gallery->id} | {$gallery->title} | {$gallery->category}\n";
    echo "   Featured: " . ($gallery->featured ? 'Yes' : 'No') . " | Carousel: " . ($gallery->carousel_pinned ? 'Yes' : 'No') . "\n";
    echo "   Image: {$gallery->image_url}\n\n";
}

echo "=== COMPLETED ===\n";
?>
