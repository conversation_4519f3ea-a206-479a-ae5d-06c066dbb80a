import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useSchoolSettings } from '../hooks/useSchoolSettings';
import LogoImage from './LogoImage';
import api from '../services/api';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { settings, loading } = useSchoolSettings();
  // Initialize with cached data
  const getCachedThemeSettings = () => {
    try {
      const cached = localStorage.getItem('themeSettings');
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error loading cached theme settings:', error);
    }
    return {
      topNavBg: '#1e40af',
      topNavText: '#ffffff',
      topNavIconColor: '#e5e7eb',
      mainNavBg: '#2563eb',
      mainNavText: '#ffffff',
      mainNavHover: '#3b82f6'
    };
  };

  const getCachedSocialMedia = () => {
    try {
      const cached = localStorage.getItem('socialMedia');
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error loading cached social media:', error);
    }
    return [];
  };

  const [socialMedia, setSocialMedia] = useState(getCachedSocialMedia());
  const [themeSettings, setThemeSettings] = useState(getCachedThemeSettings());

  // Load theme settings and social media
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const response = await api.get('/theme-settings');
        if (response.data.success) {
          const themeData = response.data.data;
          const newThemeSettings = {
            topNavBg: themeData.colors.topNavBg,
            topNavText: themeData.colors.topNavText,
            topNavIconColor: themeData.colors.topNavIconColor,
            mainNavBg: themeData.colors.mainNavBg,
            mainNavText: themeData.colors.mainNavText,
            mainNavHover: themeData.colors.mainNavHover
          };
          setThemeSettings(newThemeSettings);
          localStorage.setItem('themeSettings', JSON.stringify(newThemeSettings));
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      }
    };

    const loadSocialMedia = async () => {
      try {
        const response = await api.get('/social-media');
        if (response.data.success) {
          // Data sudah difilter dan diurutkan dari backend (getActive method)
          setSocialMedia(response.data.data);
          localStorage.setItem('socialMedia', JSON.stringify(response.data.data));
        }
      } catch (error) {
        console.error('Error loading social media:', error);
        // Keep cached data, don't overwrite
      }
    };

    loadTheme();
    loadSocialMedia();

    // Listen for social media updates
    const handleSocialMediaUpdate = () => {
      loadSocialMedia();
    };

    window.addEventListener('socialMediaUpdated', handleSocialMediaUpdate);

    // Cleanup event listener
    return () => {
      window.removeEventListener('socialMediaUpdated', handleSocialMediaUpdate);
    };
  }, []);

  return (
    <>
      {/* Top Navbar - Social Media & Address */}
      <div
        className="text-xs py-2"
        style={{
          backgroundColor: themeSettings.topNavBg,
          color: themeSettings.topNavText
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">

            {/* Social Media Links - Left side - Dynamic from Database */}
            <div className="flex items-center space-x-3">
              {/* Dynamic Social Media from Database */}
              {socialMedia.length > 0 ? (
                socialMedia.map((social) => {
                  // Use custom colors from database, fallback to black
                  const iconColor = social.icon_color || '#000000';
                  const textColor = social.text_color || '#000000';

                  // Special handling for email platform
                  const isEmail = social.platform.toLowerCase() === 'email';
                  const href = isEmail
                    ? `mailto:${social.url.replace('mailto:', '')}`
                    : social.url;
                  const target = isEmail ? '_self' : '_blank';

                  return (
                    <a
                      key={social.id}
                      href={href}
                      target={target}
                      rel={isEmail ? undefined : "noopener noreferrer"}
                      className="hover:opacity-80 transition-all duration-200 hover:scale-110"
                      title={`${social.name} - ${social.platform}`}
                    >
                      {social.icon_class ? (
                        <i
                          className={`${social.icon_class} text-sm`}
                          style={{ color: iconColor }}
                        ></i>
                      ) : (
                        <span
                          className="text-sm font-medium"
                          style={{ color: textColor }}
                        >
                          {social.name.charAt(0)}
                        </span>
                      )}
                    </a>
                  );
                })
              ) : (
                /* Default Social Media jika database kosong */
                <>
                  {/* Instagram Default */}
                  <a
                    href="https://instagram.com/sman1jakarta"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:opacity-80 transition-all duration-200 hover:scale-110"
                    style={{ color: '#e4405f' }}
                    title="Instagram"
                  >
                    <i className="fab fa-instagram text-sm"></i>
                  </a>

                  {/* YouTube Default */}
                  <a
                    href="https://youtube.com/@sman1jakarta"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:opacity-80 transition-all duration-200 hover:scale-110"
                    style={{ color: '#ff0000' }}
                    title="YouTube"
                  >
                    <i className="fab fa-youtube text-sm"></i>
                  </a>
                </>
              )}


            </div>

            {/* Address - Right side */}
            <div
              className="flex items-center justify-center min-[900px]:justify-end space-x-2 text-blue-200"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="text-center min-[900px]:text-right">
                {settings.schoolAddress ? (
                  <>
                    {/* Mobile: 8 huruf + ... */}
                    <span className="block min-[900px]:hidden">
                      {settings.schoolAddress.length > 8 ?
                        `${settings.schoolAddress.substring(0, 8)}...` :
                        settings.schoolAddress
                      }
                    </span>
                    {/* Desktop: 60 huruf + ... */}
                    <span className="hidden min-[900px]:inline">
                      {settings.schoolAddress.length > 60 ?
                        `${settings.schoolAddress.substring(0, 60)}...` :
                        settings.schoolAddress
                      }
                    </span>
                  </>
                ) : (
                  <>
                    {/* Mobile fallback: 8 huruf + ... */}
                    <span className="block min-[900px]:hidden">
                      Jl. Pend...
                    </span>
                    {/* Desktop fallback: alamat lengkap */}
                    <span className="hidden min-[900px]:inline">
                      Jl. Pendidikan No. 123, Jakarta Selatan
                    </span>
                  </>
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Navbar */}
      <nav
        className="shadow-lg"
        style={{ backgroundColor: themeSettings.mainNavBg }}
      >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link
              to="/"
              className="flex items-center text-xl font-bold"
              style={{ color: themeSettings.mainNavText }}
            >
              <LogoImage
                src={settings.logoUrl}
                alt={`Logo ${settings.schoolShortName || 'Sekolah'}`}
                className="h-10 w-10 mr-3 object-contain"
                showLoadingState={false}
              />
              <span>{settings.schoolShortName || 'Sistem Informasi Sekolah'}</span>
            </Link>
          </div>

          {/* Desktop Menu - Horizontal text links at the right (visible on screens 900px+) */}
          <div className="hidden min-[900px]:flex items-center space-x-8">
            <Link
              to="/"
              className="text-sm font-medium transition duration-300"
              style={{
                color: themeSettings.mainNavText,
                ':hover': { color: themeSettings.mainNavHover }
              }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              Home
            </Link>
            <Link
              to="/about"
              className="text-sm font-medium transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              About
            </Link>
            <Link
              to="/news"
              className="text-sm font-medium transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              Berita
            </Link>
            <Link
              to="/gallery"
              className="text-sm font-medium transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              Galeri
            </Link>
            <Link
              to="/contact"
              className="text-sm font-medium transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              Kontak
            </Link>
            <Link
              to="/students"
              className="text-sm font-medium transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              Students
            </Link>
          </div>

          {/* Mobile menu button (visible below 900px only) */}
          <div className="min-[900px]:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="focus:outline-none p-2 transition duration-300"
              style={{ color: themeSettings.mainNavText }}
              onMouseEnter={(e) => e.target.style.color = themeSettings.mainNavHover}
              onMouseLeave={(e) => e.target.style.color = themeSettings.mainNavText}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isOpen && (
          <div className="min-[900px]:hidden fixed inset-0 z-[99999]">
            {/* Background Overlay */}
            <div
              className={`fixed inset-0 bg-black transition-all duration-500 ease-out ${
                isOpen ? 'bg-opacity-50' : 'bg-opacity-0 pointer-events-none'
              }`}
              onClick={() => setIsOpen(false)}
            ></div>

            {/* Sidebar */}
            <div className={`fixed top-0 right-0 h-full w-80 bg-blue-600 shadow-2xl transform transition-all duration-500 ease-out ${
              isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
            }`}>

              {/* Sidebar Header */}
              <div className="bg-blue-700 px-6 py-4 flex items-center justify-between border-b border-blue-500">
                <div className="flex items-center">
                  <LogoImage
                    src={settings.logoUrl}
                    alt={`Logo ${settings.schoolShortName || 'Sekolah'}`}
                    className="h-10 w-10 mr-3 object-contain"
                    style={{
                      filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))'
                    }}
                    showLoadingState={false}
                  />
                  <div>
                    <h2 className="text-white font-bold text-lg">
                      {settings.schoolShortName || 'Sistem Informasi Sekolah'}
                    </h2>
                    <p className="text-blue-200 text-xs">Menu Navigasi</p>
                  </div>
                </div>

                {/* Close Button */}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-white hover:text-blue-200 p-2 rounded-full hover:bg-blue-800 transition-colors duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Navigation Links */}
              <div className="py-6">
                <nav className="space-y-2 px-4">
                  <Link
                    to="/"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span className="font-medium">Beranda</span>
                  </Link>

                  <Link
                    to="/about"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Tentang Kami</span>
                  </Link>

                  <Link
                    to="/news"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    <span className="font-medium">Berita</span>
                  </Link>

                  <Link
                    to="/gallery"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium">Galeri</span>
                  </Link>

                  <Link
                    to="/contact"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium">Kontak</span>
                  </Link>

                  <Link
                    to="/students"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <span className="font-medium">Siswa</span>
                  </Link>
                </nav>


              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
    </>
  );
};

export default Navbar;
