import React, { useState, useEffect } from 'react';
import { Button, Card, Input, Textarea } from '../ui';
import { FadeIn, StaggerContainer, StaggerItem } from '../ui/AnimatedComponents';
import AdminModal from './AdminModal';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

const AboutSettings = () => {
  const [settings, setSettings] = useState([]);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'delete'
  const { showAlert } = useAlert();

  // Form data state
  const [formData, setFormData] = useState({
    section_key: '',
    title: '',
    content: '',
    description: '',
    icon: '',
    sort_order: 0,
    is_active: true,
    image: null,
    additional_data: ''
  });

  // Image preview state
  const [imagePreview, setImagePreview] = useState(null);
  const [existingImageUrl, setExistingImageUrl] = useState(null);

  // Section types for dropdown
  const sectionTypes = [
    { key: 'vision', label: 'Visi' },
    { key: 'mission', label: 'Misi' },
    { key: 'history', label: 'Sejarah' },
    { key: 'values', label: 'Nilai-nilai' },
    { key: 'achievements', label: 'Prestasi' },
    { key: 'facilities', label: 'Fasilitas' },
    { key: 'programs', label: 'Program' },
    { key: 'staff', label: 'Tenaga Pendidik' }
  ];



  // Fetch settings
  const fetchSettings = async () => {
    try {
      setError(null);
      const response = await api.get('/admin/about-settings');
      if (response.data.success) {
        // Sort by sort_order
        const sortedSettings = response.data.data.sort((a, b) => a.sort_order - b.sort_order);
        setSettings(sortedSettings);
      } else {
        setError('Gagal memuat pengaturan about');
      }
    } catch (err) {
      console.error('Error fetching about settings:', err);
      if (err.message.includes('401') || err.message.includes('Unauthorized')) {
        setError('Anda tidak memiliki akses. Silakan login kembali.');
      } else if (err.message.includes('Route not found')) {
        setError('Route tidak ditemukan. Periksa konfigurasi backend.');
      } else {
        setError('Terjadi kesalahan saat memuat data: ' + err.message);
      }
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);



  // Reset form
  const resetForm = () => {
    setFormData({
      section_key: '',
      title: '',
      content: '',
      description: '',
      icon: '',
      sort_order: 0,
      is_active: true,
      image: null,
      additional_data: ''
    });
    setImagePreview(null);
    setExistingImageUrl(null);
  };

  // Handle modal operations
  const openModal = (mode, setting = null) => {
    setModalMode(mode);
    setSelectedSetting(setting);

    if (mode === 'edit' && setting) {
      setFormData({
        section_key: setting.section_key || '',
        title: setting.title || '',
        content: setting.content || '',
        description: setting.description || '',
        icon: setting.icon || '',
        sort_order: setting.sort_order || 0,
        is_active: setting.is_active || false,
        image: null,
        additional_data: setting.additional_data ?
          (typeof setting.additional_data === 'object' ?
            JSON.stringify(setting.additional_data, null, 2) :
            setting.additional_data) : ''
      });

      // Set existing image URL for preview
      if (setting.image_url) {
        setExistingImageUrl(`http://localhost:8000${setting.image_url}`);
      } else {
        setExistingImageUrl(null);
      }
      setImagePreview(null);
    } else if (mode === 'create') {
      resetForm();
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedSetting(null);
    setModalMode('create');
    resetForm();
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked, files } = e.target;

    if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'file') {
      const file = files[0];
      setFormData(prev => ({ ...prev, [name]: file }));

      // Create preview for new image
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
          setExistingImageUrl(null); // Clear existing image when new one is selected
        };
        reader.readAsDataURL(file);
      } else {
        setImagePreview(null);
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle additional data JSON
  const handleAdditionalDataChange = (value) => {
    setFormData(prev => ({ ...prev, additional_data: value }));
  };





  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const submitData = new FormData();
      submitData.append('section_key', formData.section_key);
      submitData.append('title', formData.title);
      submitData.append('content', formData.content);
      submitData.append('description', formData.description || '');
      submitData.append('icon', formData.icon || '');
      submitData.append('sort_order', formData.sort_order);
      submitData.append('is_active', formData.is_active ? '1' : '0');

      if (formData.image) {
        submitData.append('image', formData.image);
      }

      if (formData.additional_data) {
        submitData.append('additional_data', formData.additional_data);
      }

      let response;
      if (modalMode === 'create') {
        response = await api.post('/admin/about-settings', submitData);
      } else if (modalMode === 'edit') {
        submitData.append('_method', 'PUT');
        response = await api.post(`/admin/about-settings/${selectedSetting.id}`, submitData);
      }

      if (response.data.success) {
        await fetchSettings();
        closeModal();
        showAlert('success', modalMode === 'create' ? 'Pengaturan berhasil ditambahkan!' : 'Pengaturan berhasil diupdate!');
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      const response = await api.delete(`/admin/about-settings/${selectedSetting.id}`);
      if (response.data.success) {
        await fetchSettings();
        closeModal();
        showAlert('success', 'Pengaturan berhasil dihapus!');
      }
    } catch (err) {
      console.error('Error deleting setting:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <FadeIn direction="down" className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Pengaturan About</h2>
          <p className="text-gray-600 mt-1">Kelola konten halaman About sekolah</p>
        </div>
        <Button
          onClick={() => openModal('create')}
          variant="primary"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          }
        >
          Tambah Section
        </Button>
      </FadeIn>

      {/* Error Message */}
      {error && (
        <FadeIn className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </FadeIn>
      )}

      {/* Settings List */}
      <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settings.map((setting, index) => (
          <StaggerItem key={setting.id} delay={index * 0.1}>
            <Card className="h-full">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{setting.title || setting.section_key}</h3>
                    <p className="text-sm text-gray-500 capitalize">{setting.section_key.replace('_', ' ')}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      setting.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {setting.is_active ? 'Aktif' : 'Nonaktif'}
                    </span>
                  </div>
                </div>

                {setting.image_url && (
                  <div className="mb-4">
                    <img 
                      src={`http://localhost:8000${setting.image_url}`} 
                      alt={setting.title}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  </div>
                )}

                {setting.content && (
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">{setting.content}</p>
                )}

                <div className="flex justify-between items-center pt-4 border-t">
                  <span className="text-xs text-gray-500">Order: {setting.sort_order}</span>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => openModal('edit', setting)}
                      variant="outline"
                      size="sm"
                    >
                      Edit
                    </Button>
                    <Button
                      onClick={() => openModal('delete', setting)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      Hapus
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </StaggerItem>
        ))}
      </StaggerContainer>

      {/* Create/Edit Modal */}
      <AdminModal
        isOpen={isModalOpen && (modalMode === 'create' || modalMode === 'edit')}
        onClose={closeModal}
        title={modalMode === 'create' ? 'Tambah Section About Baru' : 'Edit Section About'}
        size="xl"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-6">
            {/* Section Key & Sort Order */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="section-key" className="block text-sm font-semibold text-gray-800">
                  Section Key *
                </label>
                {modalMode === 'create' ? (
                  <select
                    id="section-key"
                    name="section_key"
                    value={formData.section_key}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                    required
                  >
                    <option value="">Pilih Section</option>
                    {sectionTypes.map(type => (
                      <option key={type.key} value={type.key}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type="text"
                    id="section-key"
                    name="section_key"
                    value={formData.section_key}
                    disabled
                    className="w-full px-4 py-3 border-2 rounded-xl bg-gray-50 border-gray-200"
                  />
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="sort-order" className="block text-sm font-semibold text-gray-800">
                  Urutan
                </label>
                <input
                  type="number"
                  id="sort-order"
                  name="sort_order"
                  value={formData.sort_order}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                  placeholder="0"
                />
              </div>
            </div>

            {/* Title */}
            <div className="space-y-2">
              <label htmlFor="about-title" className="block text-sm font-semibold text-gray-800">
                Judul *
              </label>
              <input
                type="text"
                id="about-title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                placeholder="Masukkan judul section..."
                required
                autoComplete="off"
              />
            </div>

            {/* Description & Icon */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="description" className="block text-sm font-semibold text-gray-800">
                  Deskripsi
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                  placeholder="Masukkan deskripsi singkat..."
                  autoComplete="off"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="icon" className="block text-sm font-semibold text-gray-800">
                  Icon
                </label>
                <input
                  type="text"
                  id="icon"
                  name="icon"
                  value={formData.icon}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                  placeholder="eye, target, book-open..."
                  autoComplete="off"
                />
                <p className="text-xs text-gray-500">
                  Gunakan nama icon dari Heroicons atau Font Awesome
                </p>
              </div>
            </div>

            {/* Content */}
            <div className="space-y-2">
              <label htmlFor="content" className="block text-sm font-semibold text-gray-800">
                Konten *
              </label>
              <textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                rows={5}
                className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300"
                placeholder="Masukkan konten section..."
                required
              />
            </div>

            {/* Image Upload */}
            <div className="space-y-2">
              <label className="block text-sm font-semibold text-gray-800">
                Gambar
              </label>

              {/* Image Preview */}
              {(imagePreview || existingImageUrl) && (
                <div className="mb-4">
                  <div className="relative inline-block">
                    <img
                      src={imagePreview || existingImageUrl}
                      alt="Preview"
                      className="w-full max-w-xs h-32 object-cover rounded-lg border-2 border-gray-200"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setImagePreview(null);
                        setExistingImageUrl(null);
                        setFormData(prev => ({ ...prev, image: null }));
                        // Reset file input
                        const fileInput = document.getElementById('about-image-upload');
                        if (fileInput) fileInput.value = '';
                      }}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                    >
                      ×
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {imagePreview ? 'Gambar baru dipilih' : 'Gambar saat ini'}
                  </p>
                </div>
              )}

              <div className="flex items-center justify-center w-full">
                <label htmlFor="about-image-upload" className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg className="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="mb-2 text-sm text-gray-500">
                      <span className="font-semibold">Klik untuk upload</span> atau drag and drop
                    </p>
                    <p className="text-xs text-gray-500">PNG, JPG, JPEG (MAX. 2MB)</p>
                  </div>
                  <input
                    id="about-image-upload"
                    name="image"
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleInputChange}
                  />
                </label>
              </div>
            </div>

            {/* Additional Data */}
            <div className="space-y-2">
              <label htmlFor="additional-data" className="block text-sm font-semibold text-gray-800">
                Data Tambahan (JSON)
              </label>
              <textarea
                id="additional-data"
                name="additional_data"
                value={typeof formData.additional_data === 'object'
                  ? JSON.stringify(formData.additional_data, null, 2)
                  : formData.additional_data}
                onChange={(e) => handleAdditionalDataChange(e.target.value)}
                rows={4}
                className="w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors border-gray-200 hover:border-gray-300 font-mono text-sm"
                placeholder='{"key": "value", "array": ["item1", "item2"]}'
              />
              <p className="text-xs text-gray-500">
                Format JSON untuk data tambahan seperti statistik, timeline, dll.
              </p>
            </div>

            {/* Is Active */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="is-active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is-active" className="text-sm font-medium text-gray-700">
                Aktif
              </label>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={closeModal}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300"
            >
              Batal
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300 flex items-center"
            >
              {modalMode === 'create' ? 'Simpan' : 'Update'}
            </button>
          </div>
        </form>
      </AdminModal>

      {/* Delete Modal */}
      <AdminModal
        isOpen={isModalOpen && modalMode === 'delete'}
        onClose={closeModal}
        title="Hapus Section About"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Apakah Anda yakin ingin menghapus section <strong>{selectedSetting?.title || selectedSetting?.section_key}</strong>?
            Tindakan ini tidak dapat dibatalkan.
          </p>

          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={closeModal}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300"
            >
              Batal
            </button>
            <button
              onClick={handleDelete}
              className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300"
            >
              Hapus
            </button>
          </div>
        </div>
      </AdminModal>
    </div>
  );
};

export default AboutSettings;
