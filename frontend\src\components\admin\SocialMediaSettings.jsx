import React, { useState, useEffect } from 'react';
import { Button, Input } from '../ui';
import UniversalModal from '../ui/UniversalModal';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

// Memory cache untuk social media data
const socialMediaMemoryCache = new Map();

// Cache untuk social media data
const getCachedSocialMediaData = () => {
  try {
    // Check memory cache first
    if (socialMediaMemoryCache.has('socialMediaData')) {
      return socialMediaMemoryCache.get('socialMediaData');
    }

    // Then check localStorage
    const cached = localStorage.getItem('socialMediaData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      socialMediaMemoryCache.set('socialMediaData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }

  // Return empty array instead of null to always show table
  return [];
};

const SocialMediaSettings = () => {
  // Initialize with cached data - always show table immediately
  const cachedSocialMedia = getCachedSocialMediaData();
  const [socialMedia, setSocialMedia] = useState(cachedSocialMedia);
  const [hasTriedFetch, setHasTriedFetch] = useState(false);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSocial, setSelectedSocial] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'delete'
  const { showAlert } = useAlert();

  // Checkbox selection state
  const [selectedItems, setSelectedItems] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    platform: '',
    name: '',
    url: '',
    icon_class: '',
    icon_color: '#000000',
    text_color: '#000000',
    is_active: true,
    sort_order: 0
  });

  // Predefined social media platforms
  const platformOptions = [
    { value: 'email', label: 'Email', icon: 'fas fa-envelope', color: '#6b7280' },
    { value: 'facebook', label: 'Facebook', icon: 'fab fa-facebook-f', color: '#1877f2' },
    { value: 'instagram', label: 'Instagram', icon: 'fab fa-instagram', color: '#e4405f' },
    { value: 'twitter', label: 'Twitter', icon: 'fab fa-twitter', color: '#1da1f2' },
    { value: 'youtube', label: 'YouTube', icon: 'fab fa-youtube', color: '#ff0000' },
    { value: 'linkedin', label: 'LinkedIn', icon: 'fab fa-linkedin-in', color: '#0077b5' },
    { value: 'tiktok', label: 'TikTok', icon: 'fab fa-tiktok', color: '#000000' },
    { value: 'whatsapp', label: 'WhatsApp', icon: 'fab fa-whatsapp', color: '#25d366' },
    { value: 'telegram', label: 'Telegram', icon: 'fab fa-telegram-plane', color: '#0088cc' }
  ];

  // Checkbox selection functions
  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedItems(socialMedia.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId, checked) => {
    if (checked) {
      const newSelected = [...selectedItems, itemId];
      setSelectedItems(newSelected);
      setSelectAll(newSelected.length === socialMedia.length);
    } else {
      const newSelected = selectedItems.filter(id => id !== itemId);
      setSelectedItems(newSelected);
      setSelectAll(false);
    }
  };

  // Bulk delete function
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) {
      showAlert('Pilih item yang ingin dihapus', 'warning');
      return;
    }

    if (window.confirm(`Apakah Anda yakin ingin menghapus ${selectedItems.length} item yang dipilih?`)) {
      try {
        await Promise.all(selectedItems.map(id => api.delete(`/admin/social-media/${id}`)));
        showAlert('Item berhasil dihapus', 'success');
        setSelectedItems([]);
        setSelectAll(false);
        fetchSocialMedia();
      } catch (error) {
        showAlert('Gagal menghapus item', 'error');
      }
    }
  };

  // Fetch social media settings
  const fetchSocialMedia = async () => {
    try {
      setError(null);
      const response = await api.get('/admin/social-media');
      if (response.data.success) {
        const socialMediaData = response.data.data || [];
        setSocialMedia(socialMediaData);
        setHasTriedFetch(true);

        // Save to cache
        socialMediaMemoryCache.set('socialMediaData', socialMediaData);
        localStorage.setItem('socialMediaData', JSON.stringify(socialMediaData));

        // Reset selection when data changes
        setSelectedItems([]);
        setSelectAll(false);
      } else {
        setError('Gagal memuat pengaturan sosial media');
        setHasTriedFetch(true);
      }
    } catch (err) {
      console.error('Error fetching social media:', err);
      setError('Terjadi kesalahan saat memuat data: ' + err.message);
      setHasTriedFetch(true);
      // Keep cached data
    }
  };

  useEffect(() => {
    fetchSocialMedia();
  }, []);

  // Reset form
  const resetForm = () => {
    setFormData({
      platform: '',
      name: '',
      url: '',
      icon_class: '',
      icon_color: '#000000',
      text_color: '#000000',
      is_active: true,
      sort_order: 0
    });
  };

  // Handle modal operations
  const openModal = (mode, social = null) => {
    setModalMode(mode);
    setSelectedSocial(social);
    
    if (mode === 'edit' && social) {
      setFormData({
        platform: social.platform || '',
        name: social.name || '',
        url: social.url || '',
        icon_class: social.icon_class || '',
        icon_color: social.icon_color || '#000000',
        text_color: social.text_color || '#000000',
        is_active: social.is_active || false,
        sort_order: social.sort_order || 0
      });
    } else if (mode === 'create') {
      resetForm();
    }
    
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedSocial(null);
    setModalMode('create');
    resetForm();
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox') {
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Auto-fill icon class and URL placeholder when platform is selected
      if (name === 'platform') {
        const platform = platformOptions.find(p => p.value === value);
        if (platform) {
          setFormData(prev => ({
            ...prev,
            name: platform.label,
            icon_class: platform.icon,
            icon_color: '#000000', // Default to black
            text_color: '#000000', // Default to black
            // Clear URL when platform changes to show new placeholder
            url: ''
          }));
        }
      }
    }
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {

      let response;
      if (modalMode === 'create') {
        response = await api.post('/admin/social-media', formData);
      } else if (modalMode === 'edit') {
        response = await api.put(`/admin/social-media/${selectedSocial.id}`, formData);
      }

      if (response.data.success) {
        await fetchSocialMedia();
        closeModal();
        showAlert('success', modalMode === 'create' ? 'Sosial media berhasil ditambahkan!' : 'Sosial media berhasil diupdate!');

        // Trigger custom event to update TopNav
        window.dispatchEvent(new CustomEvent('socialMediaUpdated'));
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      const response = await api.delete(`/admin/social-media/${selectedSocial.id}`);
      if (response.data.success) {
        await fetchSocialMedia();
        closeModal();
        showAlert('success', 'Sosial media berhasil dihapus!');

        // Trigger custom event to update TopNav
        window.dispatchEvent(new CustomEvent('socialMediaUpdated'));
      }
    } catch (err) {
      console.error('Error deleting social media:', err);
      showAlert('error', 'Terjadi kesalahan: ' + (err.response?.data?.message || err.message));
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (socialId, currentStatus) => {
    // Update UI immediately (optimistic update)
    setSocialMedia(prevData =>
      prevData.map(item =>
        item.id === socialId
          ? { ...item, is_active: !currentStatus }
          : item
      )
    );

    // Trigger custom event to update TopNav immediately
    window.dispatchEvent(new CustomEvent('socialMediaUpdated'));

    try {
      // Send request to backend
      const response = await api.post(`/admin/social-media/${socialId}/toggle-active`);

      if (response.data.success) {
        showAlert('success', `Status berhasil ${!currentStatus ? 'diaktifkan' : 'dinonaktifkan'}!`);
      } else {
        // Revert if failed
        setSocialMedia(prevData =>
          prevData.map(item =>
            item.id === socialId
              ? { ...item, is_active: currentStatus }
              : item
          )
        );
        showAlert('error', 'Gagal mengubah status!');
      }
    } catch (error) {
      // Revert if error
      setSocialMedia(prevData =>
        prevData.map(item =>
          item.id === socialId
            ? { ...item, is_active: currentStatus }
            : item
        )
      );
      console.error('Error toggling status:', error);
      showAlert('error', 'Gagal mengubah status: ' + (error.response?.data?.message || error.message));
    }
  };

  // Remove loading screen - always show content immediately

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Pengaturan Sosial Media</h2>
          <p className="text-gray-600 mt-1">Kelola link sosial media yang ditampilkan di navbar</p>
        </div>
        <div className="flex gap-2">
          {selectedItems.length > 0 && (
            <Button
              onClick={handleBulkDelete}
              variant="danger"
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              }
            >
              Hapus ({selectedItems.length})
            </Button>
          )}
          <Button
            onClick={() => openModal('create')}
            variant="primary"
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            }
          >
            Tambah Sosial Media
          </Button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Social Media Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="relative px-6 py-3 w-12">
                  <input
                    type="checkbox"
                    className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  No
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Platform
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nama
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Warna
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                  URL
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                  Urutan
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {socialMedia.length === 0 && hasTriedFetch ? (
                <tr>
                  <td colSpan="9" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada Social Media</h3>
                      <p className="text-gray-500 mb-4">Mulai dengan menambahkan platform social media pertama</p>
                      <Button
                        onClick={() => openModal('create')}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium flex items-center"
                      >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Tambah Social Media
                      </Button>
                    </div>
                  </td>
                </tr>
              ) : (
                socialMedia.map((social, index) => (
                  <tr key={social.id}>
                      {/* Checkbox Column - sejajar dengan th checkbox */}
                      <td className="relative px-6 py-4 whitespace-nowrap w-12">
                        <input
                          type="checkbox"
                          className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          checked={selectedItems.includes(social.id)}
                          onChange={(e) => handleSelectItem(social.id, e.target.checked)}
                        />
                      </td>

                      {/* No Column - sejajar dengan th No */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 w-16">
                        {index + 1}
                      </td>

                      {/* Platform Column - sejajar dengan th Platform */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                              <i
                                className={`${social.icon_class} text-sm`}
                                style={{ color: platformOptions.find(p => p.value === social.platform)?.color || '#6b7280' }}
                              ></i>
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 capitalize">
                              {social.platform}
                            </div>
                          </div>
                        </div>
                      </td>

                      {/* Name Column - sejajar dengan th Nama */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {social.name}
                        </div>
                      </td>

                      {/* Color Column - sejajar dengan th Warna */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-1">
                          {/* Icon Color Preview */}
                          <div
                            className="w-5 h-5 rounded border border-gray-300 flex-shrink-0"
                            style={{ backgroundColor: social.icon_color || '#000000' }}
                            title={`Icon: ${social.icon_color || '#000000'}`}
                          ></div>
                          {/* Text Color Preview */}
                          <div
                            className="w-5 h-5 rounded border border-gray-300 flex-shrink-0"
                            style={{ backgroundColor: social.text_color || '#000000' }}
                            title={`Text: ${social.text_color || '#000000'}`}
                          ></div>
                          {/* Labels for larger screens */}
                          <div className="hidden md:flex flex-col text-xs text-gray-500">
                            <span>I: {social.icon_color || '#000000'}</span>
                            <span>T: {social.text_color || '#000000'}</span>
                          </div>
                        </div>
                      </td>

                      {/* URL Column - sejajar dengan th URL, Hidden on mobile/tablet */}
                      <td className="px-6 py-4 whitespace-nowrap hidden lg:table-cell">
                        <div className="max-w-xs">
                          <a
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 truncate block"
                            title={social.url}
                          >
                            {social.url}
                          </a>
                        </div>
                      </td>

                      {/* Status Column - sejajar dengan th Status */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {/* Toggle Switch */}
                          <label
                            className="relative inline-flex items-center cursor-pointer group"
                            title={`Geser untuk ${social.is_active ? 'nonaktifkan' : 'aktifkan'} ${social.name}`}
                          >
                            <input
                              type="checkbox"
                              checked={social.is_active}
                              onChange={() => handleToggleStatus(social.id, social.is_active)}
                              className="sr-only peer"
                            />
                            <div className={`relative w-11 h-6 rounded-full ${
                              social.is_active
                                ? 'bg-green-600'
                                : 'bg-gray-200'
                            }`}>
                              <div className={`absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 ${
                                social.is_active ? 'translate-x-5' : 'translate-x-0'
                              }`}></div>
                            </div>
                          </label>

                          {/* Status Text */}
                          <span className={`ml-3 text-xs font-medium ${
                            social.is_active ? 'text-green-800' : 'text-gray-500'
                          }`}>
                            {social.is_active ? 'Aktif' : 'Nonaktif'}
                          </span>
                        </div>
                      </td>

                      {/* Sort Order Column - sejajar dengan th Urutan, Hidden on mobile */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 hidden md:table-cell">
                        {social.sort_order}
                      </td>

                      {/* Actions Column - sejajar dengan th Aksi */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {/* Mobile: Show URL button for lg:hidden */}
                          <div className="lg:hidden">
                            <button
                              onClick={() => window.open(social.url, '_blank')}
                              className="text-blue-600 p-1"
                              title="Buka URL"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                            </button>
                          </div>

                          <button
                            onClick={() => openModal('edit', social)}
                            className="text-green-600 p-1"
                            title="Edit"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>

                          <button
                            onClick={() => openModal('delete', social)}
                            className="text-red-600 p-1"
                            title="Hapus"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Universal Modal */}
      <UniversalModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalMode === 'create' ? 'Tambah Sosial Media' :
               modalMode === 'edit' ? 'Edit Sosial Media' :
               'Hapus Sosial Media'}
        size="lg"
        footer={
          <div className="flex space-x-3">
            <Button
              onClick={closeModal}
              variant="outline"
              disabled={isSubmitting}
              className={`${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Batal
            </Button>
            {modalMode === 'delete' ? (
              <Button onClick={handleDelete} variant="danger">
                Hapus
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                variant="primary"
                disabled={isSubmitting}
                className={`${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? 'Menyimpan...' : (modalMode === 'create' ? 'Tambah' : 'Update')}
              </Button>
            )}
          </div>
        }
      >
        {modalMode === 'delete' ? (
          <div className="space-y-6">
            <p className="text-gray-600">
              Apakah Anda yakin ingin menghapus sosial media <strong>{selectedSocial?.name}</strong>?
              Tindakan ini tidak dapat dibatalkan.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Platform */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Platform *
                </label>
                <select
                  name="platform"
                  value={formData.platform}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Pilih Platform</option>
                  {platformOptions.map(platform => (
                    <option key={platform.value} value={platform.value}>
                      {platform.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Order */}
              <div className="lg:col-span-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Urutan
                </label>
                <input
                  type="number"
                  name="sort_order"
                  value={formData.sort_order}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nama *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Masukkan nama sosial media"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* URL */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL *
              </label>
              <input
                type={formData.platform === 'email' ? 'email' : 'url'}
                name="url"
                value={formData.url}
                onChange={handleInputChange}
                placeholder={
                  formData.platform === 'email'
                    ? '<EMAIL>'
                    : formData.platform === 'whatsapp'
                    ? 'https://wa.me/628123456789'
                    : formData.platform === 'telegram'
                    ? 'https://t.me/username'
                    : 'https://...'
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                {formData.platform === 'email'
                  ? 'Masukkan alamat email sekolah (contoh: <EMAIL>)'
                  : formData.platform === 'whatsapp'
                  ? 'Format: https://wa.me/628123456789 (gunakan kode negara tanpa +)'
                  : formData.platform === 'telegram'
                  ? 'Format: https://t.me/username atau https://t.me/+grouplink'
                  : 'Masukkan URL lengkap dengan https://'
                }
              </p>
            </div>

            {/* Icon Class */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Icon Class
              </label>
              <input
                type="text"
                name="icon_class"
                value={formData.icon_class}
                onChange={handleInputChange}
                placeholder="fab fa-facebook-f"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 mt-1">
                Gunakan Font Awesome icon class (contoh: fab fa-facebook-f)
              </p>
            </div>

            {/* Color Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Icon Color */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Warna Icon
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    name="icon_color"
                    value={formData.icon_color}
                    onChange={handleInputChange}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    name="icon_color"
                    value={formData.icon_color}
                    onChange={handleInputChange}
                    placeholder="#000000"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Warna untuk icon di TopNav
                </p>
              </div>

              {/* Text Color */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Warna Text
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="color"
                    name="text_color"
                    value={formData.text_color}
                    onChange={handleInputChange}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <input
                    type="text"
                    name="text_color"
                    value={formData.text_color}
                    onChange={handleInputChange}
                    placeholder="#000000"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Warna untuk text di TopNav (jika tidak ada icon)
                </p>
              </div>
            </div>

            {/* Is Active */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Aktifkan sosial media ini
              </label>
            </div>
          </div>
        )}
      </UniversalModal>
    </div>
  );
};

export default SocialMediaSettings;
