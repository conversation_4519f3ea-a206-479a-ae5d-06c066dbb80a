import { useEffect, useCallback } from 'react';

// Global logo cache
const logoCache = new Map();
const logoBlobCache = new Map();

// Preload logo when app starts
export const preloadLogo = async (logoUrl) => {
  if (!logoUrl || logoCache.has(logoUrl)) return;
  
  try {
    const response = await fetch(logoUrl);
    if (response.ok) {
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      
      // Cache in memory
      logoBlobCache.set(logoUrl, blob);
      logoCache.set(logoUrl, blobUrl);
      
      // Cache in localStorage
      const reader = new FileReader();
      reader.onload = () => {
        try {
          localStorage.setItem(`logo_${btoa(logoUrl)}`, reader.result);
        } catch {
          // Storage full, ignore
        }
      };
      reader.readAsDataURL(blob);
    }
  } catch {
    // Silent fail
  }
};

// Get cached logo URL
export const getCachedLogo = (logoUrl) => {
  if (!logoUrl) return null;
  
  // Check memory cache
  if (logoCache.has(logoUrl)) {
    return logoCache.get(logoUrl);
  }
  
  // Check localStorage
  try {
    const cached = localStorage.getItem(`logo_${btoa(logoUrl)}`);
    if (cached) {
      logoCache.set(logoUrl, cached);
      return cached;
    }
  } catch {
    // Ignore
  }
  
  // Start preloading
  preloadLogo(logoUrl);
  return logoUrl;
};

// Hook for logo caching
export const useLogoCache = (logoUrl) => {
  const preloadLogoCallback = useCallback(async (url) => {
    await preloadLogo(url);
  }, []);
  
  useEffect(() => {
    if (logoUrl) {
      preloadLogoCallback(logoUrl);
    }
  }, [logoUrl, preloadLogoCallback]);
  
  return getCachedLogo(logoUrl);
};

// Cleanup function
export const cleanupLogoCache = () => {
  logoCache.forEach((blobUrl) => {
    if (blobUrl.startsWith('blob:')) {
      URL.revokeObjectURL(blobUrl);
    }
  });
  logoCache.clear();
  logoBlobCache.clear();
};

// Auto cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupLogoCache);
}
