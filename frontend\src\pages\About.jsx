import React from 'react';
import { useSchoolSettings } from '../hooks/useSchoolSettings';
import { useAboutSettings } from '../hooks/useAboutSettings';

const About = () => {
  const { settings } = useSchoolSettings(); // Remove loading state
  const {
    aboutSettings,
    getSectionTitle,
    getSectionContent,
    getSectionImage,
    getSectionAdditionalData
  } = useAboutSettings(); // Remove loading state

  // Remove loading state - always show content immediately

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          {getSectionTitle('hero') || `Tentang ${settings?.schoolName || 'Sekolah Kami'}`}
        </h1>
        <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
          {getSectionContent('hero') || 'Membangun generasi unggul dengan pendidikan berkualitas tinggi dan berkarakter untuk masa depan yang gemilang'}
        </p>

        {/* Statistics from hero additional data */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-5xl mx-auto">
          {[
            { label: 'Siswa Aktif', value: '1200+' },
            { label: 'Guru Berpengalaman', value: '80+' },
            { label: 'Tahun Berdiri', value: '1985' },
            { label: 'Alumni Sukses', value: '5000+' }
          ].map((stat, index) => (
            <div key={index} className="text-center bg-white rounded-lg shadow-md p-6 border border-gray-100">
              <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-3">
                {stat.value}
              </div>
              <div className="text-base text-gray-700 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Vision & Mission Section */}
      <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-black">
              Visi & Misi
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto">
              Landasan filosofis yang mengarahkan setiap langkah perjalanan pendidikan kami
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Content - Kiri */}
            <div className="space-y-8">
              {/* Vision */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <svg className="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <h3 className="text-xl font-bold text-black">
                    {getSectionTitle('vision') || 'VISI'}
                  </h3>
                </div>
                <p className="text-gray-700 leading-relaxed">
                  {getSectionContent('vision') || 'Menjadi lembaga pendidikan terdepan yang menghasilkan generasi unggul, berkarakter, dan berdaya saing global dengan tetap menjunjung tinggi nilai-nilai budaya bangsa.'}
                </p>
              </div>

              {/* Mission */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <svg className="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-xl font-bold text-black">
                    {getSectionTitle('mission') || 'MISI'}
                  </h3>
                </div>
                <div className="text-gray-700 leading-relaxed">
                  {getSectionContent('mission') ? (
                    <div dangerouslySetInnerHTML={{ __html: getSectionContent('mission').replace(/\n/g, '<br>') }} />
                  ) : (
                    <ul className="space-y-3">
                      {[
                        'Menyelenggarakan pendidikan berkualitas dengan kurikulum yang inovatif dan relevan',
                        'Mengembangkan potensi peserta didik secara optimal melalui pembelajaran yang kreatif',
                        'Membentuk karakter siswa yang berakhlak mulia dan berjiwa kepemimpinan',
                        'Membangun lingkungan sekolah yang kondusif untuk pembelajaran dan pengembangan diri',
                        'Menjalin kerjasama dengan berbagai pihak untuk meningkatkan kualitas pendidikan'
                      ].map((mission, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-2 mt-1">•</span>
                          <span className="leading-relaxed">{mission}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>

            {/* Image - Kanan */}
            <div className="flex justify-center lg:justify-end">
              <img
                src={getSectionImage('vision') || getSectionImage('mission') || "https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Visi+Misi+Sekolah"}
                alt="Visi Misi Sekolah"
                className="w-full max-w-md h-80 object-cover rounded-lg shadow-lg"
                loading="eager"
                fetchPriority="high"
                decoding="sync"
                style={{
                  imageRendering: 'auto',
                  transform: 'translateZ(0)',
                  willChange: 'auto',
                  opacity: 1,
                  transition: 'none'
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* School History Section */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
          {/* Content Section - Kiri */}
          <div className="p-8 lg:p-10">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {getSectionTitle('history') || 'Sejarah Sekolah'}
            </h2>
            <div className="text-gray-700 mb-8 leading-relaxed">
              {getSectionContent('history') || 'Sekolah kami didirikan dengan visi untuk menciptakan lembaga pendidikan yang unggul dan berkarakter. Perjalanan panjang kami dimulai dari sebuah cita-cita mulia untuk memberikan pendidikan terbaik bagi generasi muda Indonesia. Dengan dedikasi tinggi dari para pendiri dan dukungan masyarakat, sekolah ini terus berkembang menjadi institusi pendidikan yang diakui dan dipercaya.'}
            </div>

            {/* Timeline */}
            <div className="space-y-4">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Timeline Penting:</h4>
              {(getSectionAdditionalData('history')?.timeline || [
                { year: '1985', event: 'Pendirian sekolah dengan 3 kelas dan 45 siswa pertama' },
                { year: '1990', event: 'Pembangunan gedung baru dan penambahan fasilitas laboratorium' },
                { year: '2000', event: 'Meraih akreditasi A dan menjadi sekolah rujukan di wilayah' },
                { year: '2010', event: 'Implementasi teknologi digital dalam pembelajaran' },
                { year: '2020', event: 'Adaptasi pembelajaran hybrid dan pengembangan platform online' }
              ]).map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600 font-bold">{item.year}</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-700 leading-relaxed">{item.event}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Image Section - Kanan */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 lg:p-10 flex items-center justify-center">
            <img
              src={getSectionImage('history') || "https://via.placeholder.com/400x300/1E40AF/FFFFFF?text=Sejarah+Sekolah"}
              alt="Sejarah Sekolah"
              className="w-full h-72 object-cover rounded-lg shadow-lg"
              loading="eager"
              fetchPriority="high"
              decoding="sync"
              style={{
                imageRendering: 'auto',
                transform: 'translateZ(0)',
                willChange: 'auto',
                opacity: 1,
                transition: 'none'
              }}
            />
          </div>
        </div>
      </div>

      {/* Facilities Section */}
      <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl shadow-lg overflow-hidden mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
          {/* Content - Kiri */}
          <div className="p-8 lg:p-10">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Fasilitas Sekolah
            </h2>
            <div className="text-gray-700 leading-relaxed mb-8">
              Sekolah kami dilengkapi dengan fasilitas modern dan lengkap untuk mendukung proses pembelajaran yang optimal. Setiap fasilitas dirancang dengan standar terbaik untuk memberikan kenyamanan dan kemudahan bagi seluruh civitas akademika.
            </div>

            {/* Facilities List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { name: 'Laboratorium Komputer', description: 'Dilengkapi dengan perangkat terbaru dan koneksi internet cepat' },
                { name: 'Perpustakaan Digital', description: 'Koleksi buku fisik dan digital yang lengkap dan terkini' },
                { name: 'Laboratorium IPA', description: 'Fasilitas praktikum lengkap untuk Fisika, Kimia, dan Biologi' },
                { name: 'Ruang Multimedia', description: 'Ruang presentasi dengan teknologi audio visual modern' },
                { name: 'Lapangan Olahraga', description: 'Lapangan basket, voli, dan futsal dengan standar internasional' },
                { name: 'Kantin Sehat', description: 'Menyediakan makanan bergizi dan higienis untuk siswa' }
              ].map((facility, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{facility.name}</h4>
                    <p className="text-gray-600 text-sm">{facility.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Image - Kanan */}
          <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-8 lg:p-10 flex items-center justify-center">
            <img
              src="https://via.placeholder.com/400x300/3B82F6/FFFFFF?text=Fasilitas+Sekolah"
              alt="Fasilitas Sekolah"
              className="w-full h-72 object-cover rounded-lg shadow-lg"
              loading="eager"
              fetchPriority="high"
              decoding="sync"
              style={{
                imageRendering: 'auto',
                transform: 'translateZ(0)',
                willChange: 'auto',
                opacity: 1,
                transition: 'none'
              }}
            />
          </div>
        </div>
      </div>

      {/* Achievements Section */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl shadow-lg overflow-hidden mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
          {/* Content - Kiri */}
          <div className="p-8 lg:p-10">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Prestasi Sekolah
            </h2>
            <div className="text-gray-700 leading-relaxed mb-8">
              Sekolah kami bangga dengan berbagai prestasi yang telah diraih oleh siswa-siswi dan guru-guru kami. Prestasi ini merupakan bukti nyata dari komitmen kami terhadap excellence dalam pendidikan dan pengembangan potensi setiap individu.
            </div>

            {/* Achievements List */}
            <div className="space-y-4">
              {[
                { title: 'Juara 1 Olimpiade Matematika Nasional', description: 'Siswa kami meraih prestasi tertinggi dalam kompetisi matematika tingkat nasional', year: '2023' },
                { title: 'Sekolah Adiwiyata Nasional', description: 'Penghargaan untuk komitmen terhadap lingkungan hidup dan pendidikan berkelanjutan', year: '2022' },
                { title: 'Juara 2 Lomba Karya Tulis Ilmiah', description: 'Prestasi dalam bidang penelitian dan penulisan karya ilmiah tingkat provinsi', year: '2023' },
                { title: 'Akreditasi A dari BAN-S/M', description: 'Pengakuan kualitas pendidikan dengan standar nasional tertinggi', year: '2021' }
              ].map((achievement, index) => (
                <div key={index} className="bg-white rounded-lg p-4 shadow-md border border-green-100">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-900 mb-1">{achievement.title}</h4>
                      <p className="text-gray-600 text-sm mb-1">{achievement.description}</p>
                      <span className="text-xs text-green-600 font-medium">{achievement.year}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Image - Kanan */}
          <div className="bg-gradient-to-br from-green-100 to-emerald-200 p-8 lg:p-10 flex items-center justify-center">
            <img
              src="https://via.placeholder.com/400x300/10B981/FFFFFF?text=Prestasi+Sekolah"
              alt="Prestasi Sekolah"
              className="w-full h-72 object-cover rounded-lg shadow-lg"
              loading="eager"
              fetchPriority="high"
              decoding="sync"
              style={{
                imageRendering: 'auto',
                transform: 'translateZ(0)',
                willChange: 'auto',
                opacity: 1,
                transition: 'none'
              }}
            />
          </div>
        </div>
      </div>

      {/* Dynamic Sections - Always show immediately */}
      {aboutSettings && Object.keys(aboutSettings).length > 0 && (
        <>
          {Object.entries(aboutSettings).map(([sectionKey, section]) => {
            // Skip sections that are already displayed above
            if (['hero', 'vision', 'mission', 'history'].includes(sectionKey)) {
              return null;
            }

            console.log(`Rendering section: ${sectionKey}`, section);

            return (
              <div key={sectionKey} className="bg-white rounded-xl shadow-lg p-8 mb-16">
                <div className="max-w-5xl mx-auto">
                  <div className="text-center mb-10">
                    <h2 className="text-3xl md:text-4xl font-bold mb-4 text-black">
                      {section.title}
                    </h2>
                    {section.description && (
                      <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                        {section.description}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div className="space-y-6">
                      <div className="text-gray-700 leading-relaxed">
                        {section.content && (
                          <div dangerouslySetInnerHTML={{ __html: section.content.replace(/\n/g, '<br>') }} />
                        )}
                      </div>

                      {/* Additional Data Display */}
                      {section.additional_data && typeof section.additional_data === 'object' && (
                        <div className="space-y-4">
                          {section.additional_data.items && Array.isArray(section.additional_data.items) && (
                            <ul className="space-y-2">
                              {section.additional_data.items.map((item, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-blue-600 mr-2 mt-1">•</span>
                                  <span className="leading-relaxed">{item}</span>
                                </li>
                              ))}
                            </ul>
                          )}

                          {section.additional_data.stats && Array.isArray(section.additional_data.stats) && (
                            <div className="grid grid-cols-2 gap-4">
                              {section.additional_data.stats.map((stat, index) => (
                                <div key={index} className="text-center bg-gray-50 rounded-lg p-4">
                                  <div className="text-2xl font-bold text-blue-600 mb-2">
                                    {stat.value}
                                  </div>
                                  <div className="text-sm text-gray-700">
                                    {stat.label}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {section.image_url && (
                      <div className="flex justify-center lg:justify-end">
                        <img
                          src={`http://localhost:8000${section.image_url}`}
                          alt={section.title}
                          className="w-full max-w-md h-80 object-cover rounded-lg shadow-lg"
                          loading="eager"
                          fetchPriority="high"
                          decoding="sync"
                          style={{
                            imageRendering: 'auto',
                            transform: 'translateZ(0)',
                            willChange: 'auto',
                            opacity: 1,
                            transition: 'none'
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </>
      )}

      {/* Contact Info Section */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-8 lg:p-10 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-black">
            Hubungi Kami
          </h2>
          <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto mb-10">
            Kami siap membantu Anda dengan informasi lebih lanjut tentang sekolah kami. Jangan ragu untuk menghubungi kami melalui berbagai saluran komunikasi yang tersedia.
          </p>

          {/* Contact Grid - Vertikal */}
          <div className="max-w-md mx-auto space-y-6">
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold mb-2 text-black">Telepon</h4>
              <p className="text-gray-600">(*************</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold mb-2 text-black">Email</h4>
              <p className="text-gray-600"><EMAIL></p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold mb-2 text-black">Alamat</h4>
              <p className="text-gray-600">Jl. Pendidikan No. 123<br />Jakarta Selatan, 12345</p>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default About;
