import React, { useState, useEffect, useCallback } from 'react';
import { useSchoolSettings } from '../hooks/useSchoolSettings';
import { useCarouselGallery } from '../hooks/useGallery';
import { useNews } from '../hooks/useNews';
// Removed useTheme import since not used
import ImageSlider from '../components/ImageSlider';
import ProfileSection from '../components/ProfileSection';
import NewsSection from '../components/NewsSection';
import ImageModal from '../components/ImageModal';

// Memory cache untuk gambar
const imageCache = new Map();
const preloadedImages = new Set();

const Home = () => {
  const { settings } = useSchoolSettings();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const {
    images: carouselGallery,
    refreshCarousel,
  } = useCarouselGallery(refreshTrigger);
  const { news } = useNews({ limit: 9, featured: false });
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Preload gambar function
  const preloadImage = useCallback((src) => {
    if (!src || preloadedImages.has(src)) return Promise.resolve();

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.loading = 'eager';
      img.fetchPriority = 'high';
      img.onload = () => {
        imageCache.set(src, img);
        preloadedImages.add(src);
        resolve(img);
      };
      img.onerror = reject;
      img.src = src;
    });
  }, []);

  // Preload carousel images
  useEffect(() => {
    if (carouselGallery.length > 0) {
      carouselGallery.forEach(image => {
        const imageUrl = image.url || image.image_url || image.path;
        if (imageUrl) {
          preloadImage(imageUrl).catch(() => {
            // Silent fail untuk preload
          });
        }
      });
    }
  }, [carouselGallery, preloadImage]);

  // Preload news images
  useEffect(() => {
    if (news.length > 0) {
      news.forEach(newsItem => {
        if (newsItem.image_url) {
          // Convert backend URL to frontend path
          let imageUrl = newsItem.image_url;
          if (imageUrl.startsWith('http://localhost:8000/images/news/')) {
            const filename = imageUrl.split('/').pop();
            imageUrl = `/images/news/${filename}`;
          }
          preloadImage(imageUrl).catch(() => {
            // Silent fail untuk preload
          });
        }
      });
    }
  }, [news, preloadImage]);

  // Global refresh & auto reload
  useEffect(() => {
    window.refreshCarousel = () => {
      refreshCarousel();
      setRefreshTrigger(prev => prev + 1);
    };
  }, [refreshCarousel]);

  const autoRefreshGallery = useCallback(() => {
    refreshCarousel();
    setRefreshTrigger(prev => prev + 1);
  }, [refreshCarousel]);

  useEffect(() => {
    const interval = setInterval(autoRefreshGallery, 60000);
    return () => clearInterval(interval);
  }, [autoRefreshGallery]);

  // ✔ Perbaikan handleImageClick
  const handleImageClick = (image, index) => {
    if (image && typeof index === 'number') {
      setSelectedImage(image);
      setSelectedImageIndex(index);
    }
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const renderCarouselContent = () => {
    // Always show carousel immediately - even if empty, show placeholder with default images
    if (carouselGallery.length === 0) {
      // Show default carousel with school building image instead of empty state
      const defaultImages = [
        {
          id: 'default-1',
          title: 'Selamat Datang di Sistem Informasi Sekolah',
          description: 'Platform digital untuk informasi sekolah terkini',
          url: '/images/school-building.jpg',
          image_url: '/images/school-building.jpg'
        }
      ];

      return (
        <ImageSlider
          images={defaultImages}
          onImageClick={handleImageClick}
          autoPlay={true}
          interval={5000}
        />
      );
    }

    return (
      <ImageSlider
        images={carouselGallery}
        autoPlay={true}
        showButtons={true}
        className="rounded-xl overflow-hidden"
        onImageClick={handleImageClick} // ✔ dipanggil di komponen
      />
    );
  };

  return (
    <div className="min-h-screen">
      {/* Hero Carousel Section - Full Width */}
      <section className="mb-16">
        <div className="w-full">
          {renderCarouselContent()}
        </div>
      </section>

      {/* Profile Section - Full Width */}
      <ProfileSection settings={settings} />

      {/* News Section - Full Width */}
      <NewsSection news={news} settings={settings} />

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          images={carouselGallery}
          currentIndex={selectedImageIndex}
          onClose={closeModal}
        />
      )}
    </div>
  );
};

export default Home;
