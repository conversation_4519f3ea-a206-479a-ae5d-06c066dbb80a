import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

// Memory cache untuk news
const newsPageMemoryCache = new Map();
const preloadedNewsImages = new Set();
const newsImageCache = new Map(); // Cache untuk blob URLs
const newsBlobCache = new Map(); // Cache untuk image blobs
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Cleanup blob URLs on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    newsImageCache.forEach((blobUrl) => {
      if (blobUrl.startsWith('blob:')) {
        URL.revokeObjectURL(blobUrl);
      }
    });
  });
}

const News = () => {
  // Initialize with cached data
  const getCachedNews = (category) => {
    try {
      const cacheKey = `newsPage_${category}`;

      // Check memory cache first
      if (newsPageMemoryCache.has(cacheKey)) {
        return newsPageMemoryCache.get(cacheKey);
      }

      // Then check localStorage
      const cached = localStorage.getItem(cacheKey);
      if (cached) {
        const parsedData = JSON.parse(cached);
        // Store in memory cache for faster access
        newsPageMemoryCache.set(cacheKey, parsedData);
        return parsedData;
      }
    } catch {
      // Silent error handling
    }
    return [];
  };

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [news, setNews] = useState(getCachedNews('all'));
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const categories = ['all', 'Prestasi', 'Akademik', 'Kegiatan', 'Pengumuman', 'Fasilitas'];

  // Aggressive news image caching function
  const cacheNewsImage = useCallback(async (src) => {
    if (!src || newsImageCache.has(src)) return newsImageCache.get(src) || src;

    try {
      // Try to fetch and cache as blob for instant display
      const response = await fetch(src);
      if (response.ok) {
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // Cache both blob and blob URL
        newsBlobCache.set(src, blob);
        newsImageCache.set(src, blobUrl);
        preloadedNewsImages.add(src);

        // Also cache in localStorage as base64 for persistence
        const reader = new FileReader();
        reader.onload = () => {
          try {
            localStorage.setItem(`news_img_cache_${btoa(src)}`, reader.result);
          } catch {
            // Storage full, ignore
          }
        };
        reader.readAsDataURL(blob);

        return blobUrl;
      }
    } catch {
      // Fallback to original src
    }

    newsImageCache.set(src, src);
    return src;
  }, []);

  // Get cached news image URL
  const getCachedNewsImageUrl = useCallback((src) => {
    if (!src) return '/placeholder-news.jpg';

    // Check memory cache first
    if (newsImageCache.has(src)) {
      return newsImageCache.get(src);
    }

    // Check localStorage cache
    try {
      const cached = localStorage.getItem(`news_img_cache_${btoa(src)}`);
      if (cached) {
        newsImageCache.set(src, cached);
        return cached;
      }
    } catch {
      // Ignore localStorage errors
    }

    // Start caching process and return original URL
    cacheNewsImage(src);
    return src;
  }, [cacheNewsImage]);

  // Cache cached news images immediately on mount
  useEffect(() => {
    const initialNews = getCachedNews('all');
    if (initialNews.length > 0) {
      initialNews.forEach(async (newsItem) => {
        const imageUrl = getImageUrl(newsItem);
        if (imageUrl && imageUrl !== '/placeholder-news.jpg') {
          await cacheNewsImage(imageUrl);
        }
      });
    }
  }, [cacheNewsImage]); // Run when cacheNewsImage is available

  // Function to get correct image URL
  const getImageUrl = (newsItem) => {
    if (!newsItem || !newsItem.image_url) {
      return '/placeholder-image.jpg';
    }

    const imageUrl = newsItem.image_url;

    // If it's already a full URL from backend (http://localhost:8000/images/news/), convert to frontend path
    if (imageUrl.startsWith('http://localhost:8000/images/news/')) {
      const filename = imageUrl.split('/').pop();
      const frontendPath = `/images/news/${filename}`;
      return frontendPath;
    }

    // If it's already a full URL (starts with http), return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // If it's /images/news path (frontend public format), use directly
    if (imageUrl.startsWith('/images/news/')) {
      return imageUrl;
    }

    // Default fallback
    return '/placeholder-image.jpg';
  };

  // Fetch news data from API
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setError(null);

        const response = await fetch(`${API_BASE_URL}/news`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          const newNews = data.data || [];

          // Only update if data actually changed
          const currentNewsStr = JSON.stringify(news);
          const newNewsStr = JSON.stringify(newNews);

          if (currentNewsStr !== newNewsStr) {
            setNews(newNews);

            // Cache in both memory and localStorage
            const cacheKey = `newsPage_all`;
            newsPageMemoryCache.set(cacheKey, newNews);
            localStorage.setItem(cacheKey, JSON.stringify(newNews));
          }
        } else {
          throw new Error(data.message || 'Failed to fetch news');
        }
      } catch (err) {
        setError(err.message);
        // Keep cached news, don't overwrite them
      }
    };

    fetchNews();
  }, [news]);

  // Update news when category changes
  useEffect(() => {
    const cachedNews = getCachedNews('all');
    if (cachedNews.length > 0 && news.length === 0) {
      setNews(cachedNews);
    }
  }, [news.length]);

  // Cache news images immediately
  useEffect(() => {
    if (news.length > 0) {
      // Cache all images in parallel for instant display
      news.forEach(async (newsItem) => {
        const imageUrl = getImageUrl(newsItem);
        if (imageUrl && imageUrl !== '/placeholder-news.jpg') {
          await cacheNewsImage(imageUrl);
        }
      });
    }
  }, [news, cacheNewsImage]);

  // Filter news berdasarkan kategori
  const filteredNews = news.filter(item => {
    return selectedCategory === 'all' || item.category === selectedCategory;
  });

  // Handle click to detail page and increment views
  const handleNewsClick = async (newsId) => {
    try {
      // Increment views
      await fetch(`${API_BASE_URL}/news/${newsId}/views`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Failed to increment views:', error);
    }

    // Navigate to news detail
    navigate(`/news/${newsId}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <h1 className="text-3xl font-bold text-gray-800 text-center">
            Berita & Artikel
          </h1>
        </div>
      </div>

      {/* Category Filter */}
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className="flex flex-wrap justify-center gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category === 'all' ? 'Semua' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Remove loading state - always show content immediately */}

      {/* Error State */}
      {error && (
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <p className="text-red-600">Error: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Coba Lagi
            </button>
          </div>
        </div>
      )}

      {/* News Grid - Always show immediately */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Always show news grid if there's any data, even if filtered result is empty */}
        {news.length > 0 ? (
          <>
            {filteredNews.length > 0 ? (
              <div className="grid grid-cols-1 min-[900px]:grid-cols-3 gap-6">
                {filteredNews.map((newsItem) => (
                  <div
                    key={newsItem.id}
                    onClick={() => handleNewsClick(newsItem.id)}
                    className="bg-white border-2 border-gray-300 rounded-lg shadow-sm hover:shadow-md hover:border-blue-400 transition-all duration-200 cursor-pointer"
                  >
                    {/* Image */}
                    <div className="aspect-[3/2] overflow-hidden rounded-t-lg bg-gray-100">
                      <img
                        src={getCachedNewsImageUrl(getImageUrl(newsItem))}
                        alt={newsItem.title}
                        className="w-full h-full object-cover"
                        loading="eager"
                        fetchPriority="high"
                        decoding="sync"
                        style={{
                          imageRendering: 'auto',
                          transform: 'translateZ(0)',
                          willChange: 'auto',
                          opacity: 1,
                          transition: 'none',
                          display: 'block'
                        }}
                        onLoad={(e) => {
                          e.target.style.opacity = '1';
                          e.target.style.visibility = 'visible';
                        }}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = '/placeholder-news.jpg';
                        }}
                      />
                    </div>

                  {/* Content */}
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-2 line-clamp-2">
                      {newsItem.title}
                    </h3>
                    <div className="flex justify-between items-center mb-2">
                      <p className="text-sm text-gray-500">
                        {newsItem.category}
                      </p>
                      {newsItem.featured && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                          Unggulan
                        </span>
                      )}
                    </div>
                    {newsItem.excerpt && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                        {newsItem.excerpt}
                      </p>
                    )}
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <div className="flex items-center space-x-2">
                        <span>{newsItem.author?.name || 'Admin'}</span>
                        {newsItem.views !== undefined && (
                          <>
                            <span>•</span>
                            <span className="flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              {newsItem.views || 0}
                            </span>
                          </>
                        )}
                      </div>
                      <span>{new Date(newsItem.published_at).toLocaleDateString('id-ID')}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Tidak ada berita untuk kategori "{selectedCategory}"
                </h3>
                <p className="text-gray-500">
                  Silakan pilih kategori lain atau kembali ke "Semua" untuk melihat semua berita.
                </p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Belum Ada Berita
            </h3>
            <p className="text-gray-500">
              Belum ada berita yang tersedia saat ini. Silakan coba lagi nanti.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default News;
