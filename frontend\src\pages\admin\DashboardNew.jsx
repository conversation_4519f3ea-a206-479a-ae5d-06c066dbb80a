import { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import WelcomeModal from '../../components/admin/WelcomeModal';
import { api } from '../../services/api';
import { useAdminAuth } from '../../hooks/useAdminAuth';

// Memory cache untuk dashboard data
const dashboardMemoryCache = new Map();

// Cache untuk dashboard data dengan default values
const getCachedDashboardData = () => {
  try {
    // Check memory cache first
    if (dashboardMemoryCache.has('dashboardData')) {
      return dashboardMemoryCache.get('dashboardData');
    }
    
    // Then check localStorage
    const cached = localStorage.getItem('dashboardData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      dashboardMemoryCache.set('dashboardData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }
  
  // Default data dengan Font Awesome icons
  return {
    stats: [
      {
        title: 'Total Siswa',
        value: '1,234',
        change: '+12%',
        changeType: 'increase',
        icon: 'fas fa-users',
        bgColor: 'bg-gradient-to-br from-blue-500 to-blue-600'
      },
      {
        title: 'Total Guru',
        value: '89',
        change: '+5%',
        changeType: 'increase',
        icon: 'fas fa-chalkboard-teacher',
        bgColor: 'bg-gradient-to-br from-green-500 to-green-600'
      },
      {
        title: 'Total Kelas',
        value: '36',
        change: '+2%',
        changeType: 'increase',
        icon: 'fas fa-school',
        bgColor: 'bg-gradient-to-br from-purple-500 to-purple-600'
      },
      {
        title: 'Prestasi',
        value: '127',
        change: '+18%',
        changeType: 'increase',
        icon: 'fas fa-trophy',
        bgColor: 'bg-gradient-to-br from-amber-500 to-orange-500'
      }
    ],
    recentNews: [
      {
        id: 1,
        title: 'Pengumuman Libur Semester',
        excerpt: 'Libur semester akan dimulai tanggal 15 Januari 2024',
        date: '2024-01-15',
        status: 'published',
        views: 245
      },
      {
        id: 2,
        title: 'Pendaftaran Ekstrakurikuler',
        excerpt: 'Pendaftaran ekstrakurikuler dibuka untuk semester baru',
        date: '2024-01-14',
        status: 'published',
        views: 189
      },
      {
        id: 3,
        title: 'Ujian Tengah Semester',
        excerpt: 'Jadwal ujian tengah semester telah dirilis',
        date: '2024-01-13',
        status: 'published',
        views: 156
      }
    ],
    recentActivities: [
      {
        id: 1,
        action: 'Berita baru ditambahkan',
        user: 'Admin',
        time: '2 jam yang lalu',
        type: 'create'
      },
      {
        id: 2,
        action: 'Galeri foto diperbarui',
        user: 'Admin',
        time: '4 jam yang lalu',
        type: 'update'
      },
      {
        id: 3,
        action: 'Pengumuman baru dipublikasi',
        user: 'Admin',
        time: '6 jam yang lalu',
        type: 'publish'
      }
    ]
  };
};

const DashboardNew = () => {
  const { isAuthenticated, isLoading: authLoading, redirectToLogin } = useAdminAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [userInfo, setUserInfo] = useState({ role: 'Admin', name: 'Administrator' });
  
  // Initialize with cached data - always show content immediately
  const cachedData = getCachedDashboardData();
  const [stats, setStats] = useState(cachedData.stats || []);
  const [recentNews, setRecentNews] = useState(cachedData.recentNews || []);
  const [recentActivities, setRecentActivities] = useState(cachedData.recentActivities || []);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      redirectToLogin();
    }
  }, [authLoading, isAuthenticated, redirectToLogin]);

  // Check if user just logged in and show welcome modal
  useEffect(() => {
    const justLoggedIn = sessionStorage.getItem('justLoggedIn');
    const authData = localStorage.getItem('adminAuth');

    if (justLoggedIn && authData) {
      try {
        const parsedAuth = JSON.parse(authData);
        setUserInfo({
          role: parsedAuth.role || 'Admin',
          name: parsedAuth.username || 'Administrator'
        });
        setShowWelcomeModal(true);
        sessionStorage.removeItem('justLoggedIn');
      } catch {
        // Silent error handling
      }
    }
  }, []);

  // Fetch dashboard data in background
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch stats
        try {
          const statsResponse = await api.get('/dashboard/stats');
          if (statsResponse.data.success && Array.isArray(statsResponse.data.data)) {
            setStats(statsResponse.data.data);
          }
        } catch {
          // Keep cached stats
        }

        // Fetch recent news
        try {
          const newsResponse = await api.get('/dashboard/recent-news');
          if (newsResponse.data.success && Array.isArray(newsResponse.data.data)) {
            setRecentNews(newsResponse.data.data);
          }
        } catch {
          // Keep cached news
        }

        // Fetch recent activities
        try {
          const activitiesResponse = await api.get('/dashboard/recent-activities');
          if (activitiesResponse.data.success && Array.isArray(activitiesResponse.data.data)) {
            setRecentActivities(activitiesResponse.data.data);
          }
        } catch {
          // Keep cached activities
        }

        // Save current data to cache
        const currentData = {
          stats: stats,
          recentNews: recentNews,
          recentActivities: recentActivities
        };
        dashboardMemoryCache.set('dashboardData', currentData);
        localStorage.setItem('dashboardData', JSON.stringify(currentData));

      } catch {
        // Keep cached data if API fails
      }
    };

    if (isAuthenticated && !authLoading) {
      fetchDashboardData();
    }
  }, [isAuthenticated, authLoading, stats, recentNews, recentActivities]);

  // Only redirect if not authenticated, no loading screen
  if (!authLoading && !isAuthenticated) {
    return null;
  }

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-8">
            <div className="max-w-7xl mx-auto">
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600 mt-2">Selamat datang di panel admin SMA Negeri 1 Jakarta</p>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Main Layout - Responsive */}
          <div className="grid grid-cols-1 min-[900px]:grid-cols-4 gap-8">
            
            {/* Stats Cards - Left side for >900px, top for smaller screens */}
            <div className="min-[900px]:col-span-1 order-1">
              <div className="space-y-6">
                {stats.map((stat, index) => (
                  <div key={index} className="group">
                    <div className={`${stat.bgColor} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 text-white relative overflow-hidden`}>
                      {/* Background Pattern */}
                      <div className="absolute top-0 right-0 -mt-4 -mr-4 opacity-20">
                        <i className={`${stat.icon} text-6xl`}></i>
                      </div>
                      
                      {/* Content */}
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-4">
                          <div className="p-3 bg-white bg-opacity-20 rounded-lg backdrop-blur-sm">
                            <i className={`${stat.icon} text-xl text-white`}></i>
                          </div>
                          
                          {stat.change && (
                            <div className="flex items-center space-x-1 px-3 py-1 bg-white bg-opacity-20 rounded-full text-xs font-medium backdrop-blur-sm">
                              <i className={`fas ${stat.changeType === 'increase' ? 'fa-arrow-up' : 'fa-arrow-down'} text-xs`}></i>
                              <span>{stat.change}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <h3 className="text-sm font-medium text-white text-opacity-90 uppercase tracking-wide">
                            {stat.title}
                          </h3>
                          <p className="text-3xl font-bold text-white group-hover:scale-105 transition-transform">
                            {stat.value}
                          </p>
                          {stat.change && (
                            <p className="text-xs text-white text-opacity-75">dari bulan lalu</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Content Area - Right side for >900px, bottom for smaller screens */}
            <div className="min-[900px]:col-span-3 order-2">
              <div className="space-y-8">

                {/* Recent News & Activities */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Recent News */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <i className="fas fa-newspaper text-blue-600"></i>
                          </div>
                          <h2 className="text-lg font-semibold text-gray-900">Berita Terbaru</h2>
                        </div>
                        <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                          Lihat Semua
                        </button>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="space-y-4">
                        {recentNews.map((news) => (
                          <div key={news.id} className="group cursor-pointer">
                            <div className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors">
                              <div className="flex-shrink-0">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                  <i className="fas fa-newspaper text-white"></i>
                                </div>
                              </div>

                              <div className="flex-1 min-w-0">
                                <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                                  {news.title}
                                </h3>
                                <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                                  {news.excerpt}
                                </p>

                                <div className="flex items-center justify-between mt-3">
                                  <div className="flex items-center space-x-4 text-xs text-gray-400">
                                    <div className="flex items-center space-x-1">
                                      <i className="fas fa-clock"></i>
                                      <span>{formatDate(news.date)}</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      <i className="fas fa-eye"></i>
                                      <span>{news.views}</span>
                                    </div>
                                  </div>

                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    news.status === 'published'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {news.status === 'published' ? 'Dipublikasi' : 'Draft'}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {recentNews.length === 0 && (
                        <div className="text-center py-8">
                          <i className="fas fa-newspaper text-4xl text-gray-300 mb-3"></i>
                          <p className="text-gray-500">Belum ada berita terbaru</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recent Activities */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-green-100 rounded-lg">
                            <i className="fas fa-clock text-green-600"></i>
                          </div>
                          <h2 className="text-lg font-semibold text-gray-900">Aktivitas Terbaru</h2>
                        </div>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="space-y-4">
                        {recentActivities.map((activity) => (
                          <div key={activity.id} className="flex items-start space-x-4">
                            <div className="flex-shrink-0">
                              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                activity.type === 'create' ? 'bg-green-100' :
                                activity.type === 'update' ? 'bg-blue-100' :
                                activity.type === 'publish' ? 'bg-purple-100' : 'bg-gray-100'
                              }`}>
                                <div className={`w-3 h-3 rounded-full ${
                                  activity.type === 'create' ? 'bg-green-500' :
                                  activity.type === 'update' ? 'bg-blue-500' :
                                  activity.type === 'publish' ? 'bg-purple-500' : 'bg-gray-500'
                                }`}></div>
                              </div>
                            </div>

                            <div className="flex-1 min-w-0">
                              <p className="text-sm text-gray-900">
                                <span className="font-medium">{activity.user}</span>
                                {' '}
                                <span className="text-gray-600">{activity.action}</span>
                              </p>
                              <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                            </div>
                          </div>
                        ))}
                      </div>

                      {recentActivities.length === 0 && (
                        <div className="text-center py-8">
                          <i className="fas fa-clock text-4xl text-gray-300 mb-3"></i>
                          <p className="text-gray-500">Belum ada aktivitas terbaru</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-6">Aksi Cepat</h2>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all group">
                      <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors mb-3">
                        <i className="fas fa-newspaper text-blue-600"></i>
                      </div>
                      <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">Tambah Berita</span>
                    </button>

                    <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all group">
                      <div className="p-3 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors mb-3">
                        <i className="fas fa-users text-green-600"></i>
                      </div>
                      <span className="text-sm font-medium text-gray-700 group-hover:text-green-700">Kelola Siswa</span>
                    </button>

                    <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-all group">
                      <div className="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors mb-3">
                        <i className="fas fa-chalkboard-teacher text-purple-600"></i>
                      </div>
                      <span className="text-sm font-medium text-gray-700 group-hover:text-purple-700">Kelola Guru</span>
                    </button>

                    <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-amber-300 hover:bg-amber-50 transition-all group">
                      <div className="p-3 bg-amber-100 rounded-lg group-hover:bg-amber-200 transition-colors mb-3">
                        <i className="fas fa-school text-amber-600"></i>
                      </div>
                      <span className="text-sm font-medium text-gray-700 group-hover:text-amber-700">Kelola Kelas</span>
                    </button>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Welcome Modal */}
      <WelcomeModal
        isOpen={showWelcomeModal}
        onClose={() => setShowWelcomeModal(false)}
        userInfo={userInfo}
      />
    </AdminLayout>
  );
};

export default DashboardNew;
