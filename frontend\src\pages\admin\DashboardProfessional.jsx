import { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import WelcomeModal from '../../components/admin/WelcomeModal';
import { api } from '../../services/api';
import { useAdminAuth } from '../../hooks/useAdminAuth';

// Memory cache untuk dashboard data
const dashboardMemoryCache = new Map();

// Cache untuk dashboard data dengan default values
const getCachedDashboardData = () => {
  try {
    // Check memory cache first
    if (dashboardMemoryCache.has('dashboardData')) {
      return dashboardMemoryCache.get('dashboardData');
    }
    
    // Then check localStorage
    const cached = localStorage.getItem('dashboardData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      dashboardMemoryCache.set('dashboardData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }
  
  // Default data
  return {
    stats: [
      {
        title: 'Total Berita',
        value: '156',
        change: '+12',
        changePercent: '+8.2%',
        changeType: 'increase',
        icon: 'fas fa-newspaper',
        bgColor: 'bg-gradient-to-br from-blue-500 to-blue-600',
        lightBg: 'bg-blue-50',
        textColor: 'text-blue-600'
      },
      {
        title: 'Total Galeri',
        value: '89',
        change: '+5',
        changePercent: '+5.9%',
        changeType: 'increase',
        icon: 'fas fa-images',
        bgColor: 'bg-gradient-to-br from-green-500 to-green-600',
        lightBg: 'bg-green-50',
        textColor: 'text-green-600'
      },
      {
        title: 'Total Views',
        value: '12.4K',
        change: '+1.2K',
        changePercent: '+10.5%',
        changeType: 'increase',
        icon: 'fas fa-eye',
        bgColor: 'bg-gradient-to-br from-purple-500 to-purple-600',
        lightBg: 'bg-purple-50',
        textColor: 'text-purple-600'
      },
      {
        title: 'Total Pengguna',
        value: '2.1K',
        change: '+89',
        changePercent: '+4.3%',
        changeType: 'increase',
        icon: 'fas fa-users',
        bgColor: 'bg-gradient-to-br from-amber-500 to-orange-500',
        lightBg: 'bg-amber-50',
        textColor: 'text-amber-600'
      }
    ],
    recentNews: [
      {
        id: 1,
        title: 'Pengumuman Libur Semester Genap 2024',
        excerpt: 'Libur semester genap akan dimulai tanggal 15 Juni 2024 dan berakhir pada tanggal 15 Juli 2024.',
        date: '2024-01-15T10:30:00Z',
        status: 'published',
        views: 1245,
        author: 'Admin Sekolah',
        category: 'Pengumuman'
      },
      {
        id: 2,
        title: 'Pendaftaran Ekstrakurikuler Semester Baru',
        excerpt: 'Pendaftaran ekstrakurikuler untuk semester baru telah dibuka. Siswa dapat mendaftar melalui website sekolah.',
        date: '2024-01-14T14:20:00Z',
        status: 'published',
        views: 892,
        author: 'Koordinator Ekskul',
        category: 'Kegiatan'
      },
      {
        id: 3,
        title: 'Jadwal Ujian Tengah Semester',
        excerpt: 'Jadwal ujian tengah semester telah dirilis. Siswa diharapkan mempersiapkan diri dengan baik.',
        date: '2024-01-13T09:15:00Z',
        status: 'published',
        views: 1567,
        author: 'Wakil Kepala Sekolah',
        category: 'Akademik'
      },
      {
        id: 4,
        title: 'Workshop Digital Marketing untuk Guru',
        excerpt: 'Sekolah mengadakan workshop digital marketing untuk meningkatkan kompetensi guru dalam era digital.',
        date: '2024-01-12T16:45:00Z',
        status: 'draft',
        views: 234,
        author: 'Tim IT',
        category: 'Pelatihan'
      }
    ],
    recentActivities: [
      {
        id: 1,
        action: 'Menambahkan berita baru',
        description: 'Pengumuman Libur Semester Genap 2024',
        user: 'Admin Sekolah',
        time: '2024-01-15T10:30:00Z',
        type: 'create',
        icon: 'fas fa-plus-circle',
        color: 'text-green-600'
      },
      {
        id: 2,
        action: 'Memperbarui galeri foto',
        description: 'Menambahkan 15 foto kegiatan OSIS',
        user: 'Koordinator OSIS',
        time: '2024-01-15T08:15:00Z',
        type: 'update',
        icon: 'fas fa-edit',
        color: 'text-blue-600'
      },
      {
        id: 3,
        action: 'Mempublikasi pengumuman',
        description: 'Jadwal Ujian Tengah Semester',
        user: 'Wakil Kepala Sekolah',
        time: '2024-01-14T16:20:00Z',
        type: 'publish',
        icon: 'fas fa-bullhorn',
        color: 'text-purple-600'
      },
      {
        id: 4,
        action: 'Menghapus konten',
        description: 'Berita lama yang sudah tidak relevan',
        user: 'Admin Sekolah',
        time: '2024-01-14T14:10:00Z',
        type: 'delete',
        icon: 'fas fa-trash',
        color: 'text-red-600'
      },
      {
        id: 5,
        action: 'Backup database',
        description: 'Backup otomatis database sekolah',
        user: 'System',
        time: '2024-01-14T02:00:00Z',
        type: 'system',
        icon: 'fas fa-database',
        color: 'text-gray-600'
      }
    ]
  };
};

const DashboardProfessional = () => {
  const { isAuthenticated, isLoading: authLoading, redirectToLogin } = useAdminAuth();
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [userInfo, setUserInfo] = useState({ role: 'Admin', name: 'Administrator' });
  
  // Initialize with cached data - always show content immediately
  const cachedData = getCachedDashboardData();
  const [stats, setStats] = useState(cachedData.stats || []);
  const [recentNews, setRecentNews] = useState(cachedData.recentNews || []);
  const [recentActivities, setRecentActivities] = useState(cachedData.recentActivities || []);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      redirectToLogin();
    }
  }, [authLoading, isAuthenticated, redirectToLogin]);

  // Check if user just logged in and show welcome modal
  useEffect(() => {
    const justLoggedIn = sessionStorage.getItem('justLoggedIn');
    const authData = localStorage.getItem('adminAuth');

    if (justLoggedIn && authData) {
      try {
        const parsedAuth = JSON.parse(authData);
        setUserInfo({
          role: parsedAuth.role || 'Admin',
          name: parsedAuth.username || 'Administrator'
        });
        setShowWelcomeModal(true);
        sessionStorage.removeItem('justLoggedIn');
      } catch {
        // Silent error handling
      }
    }
  }, []);

  // Fetch dashboard data from database
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch stats from database
        try {
          const statsResponse = await api.get('/dashboard/stats');
          if (statsResponse.data.success && Array.isArray(statsResponse.data.data)) {
            setStats(statsResponse.data.data);
          }
        } catch (error) {
          console.log('Stats endpoint not available, using cached data:', error.message);
        }

        // Fetch recent news from database
        try {
          const newsResponse = await api.get('/dashboard/recent-news');
          if (newsResponse.data.success && Array.isArray(newsResponse.data.data)) {
            setRecentNews(newsResponse.data.data);
          }
        } catch (error) {
          console.log('Recent news endpoint not available, using cached data:', error.message);
        }

        // Fetch recent activities from database
        try {
          const activitiesResponse = await api.get('/dashboard/recent-activities');
          if (activitiesResponse.data.success && Array.isArray(activitiesResponse.data.data)) {
            setRecentActivities(activitiesResponse.data.data);
          }
        } catch (error) {
          console.log('Recent activities endpoint not available, using cached data:', error.message);
        }

        // Save current data to cache
        const currentData = {
          stats: stats,
          recentNews: recentNews,
          recentActivities: recentActivities
        };
        dashboardMemoryCache.set('dashboardData', currentData);
        localStorage.setItem('dashboardData', JSON.stringify(currentData));

      } catch (error) {
        console.log('Dashboard data fetch failed, using cached data:', error.message);
      }
    };

    if (isAuthenticated && !authLoading) {
      fetchDashboardData();
    }
  }, [isAuthenticated, authLoading, stats, recentNews, recentActivities]);

  // Only redirect if not authenticated, no loading screen
  if (!authLoading && !isAuthenticated) {
    return null;
  }

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Baru saja';
    if (diffInMinutes < 60) return `${diffInMinutes} menit yang lalu`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} jam yang lalu`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} hari yang lalu`;
    
    return formatDate(dateString);
  };

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-8">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                  <p className="text-gray-600 mt-2">Selamat datang di panel admin SMA Negeri 1 Jakarta</p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Terakhir diperbarui</p>
                    <p className="text-sm font-medium text-gray-900">{formatDate(new Date())}</p>
                  </div>
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i className="fas fa-sync-alt mr-2"></i>
                    Refresh Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <div key={index} className="group">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300 overflow-hidden">
                  {/* Card Header with Gradient */}
                  <div className={`${stat.bgColor} px-6 py-4`}>
                    <div className="flex items-center justify-between">
                      <div className="text-white">
                        <p className="text-white text-opacity-90 text-sm font-medium">{stat.title}</p>
                        <p className="text-3xl font-bold mt-1">{stat.value}</p>
                      </div>
                      <div className="text-white text-opacity-80">
                        <i className={`${stat.icon} text-3xl`}></i>
                      </div>
                    </div>
                  </div>
                  
                  {/* Card Body */}
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                          stat.changeType === 'increase' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          <i className={`fas ${stat.changeType === 'increase' ? 'fa-arrow-up' : 'fa-arrow-down'} text-xs`}></i>
                          <span>{stat.change}</span>
                        </div>
                        <span className="text-xs text-gray-500">dari bulan lalu</span>
                      </div>
                      <span className={`text-sm font-semibold ${
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.changePercent}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

            {/* Recent News */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <i className="fas fa-newspaper text-blue-600"></i>
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900">Berita Terbaru</h2>
                      <p className="text-sm text-gray-500">Konten terbaru yang dipublikasi</p>
                    </div>
                  </div>
                  <button className="text-sm text-blue-600 hover:text-blue-700 font-medium hover:bg-blue-50 px-3 py-1 rounded-lg transition-colors">
                    Lihat Semua
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {recentNews.map((news) => (
                    <div key={news.id} className="group cursor-pointer">
                      <div className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors border border-transparent hover:border-gray-200">
                        <div className="flex-shrink-0">
                          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i className="fas fa-newspaper text-white text-lg"></i>
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                              {news.title}
                            </h3>
                            <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                              news.status === 'published'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {news.status === 'published' ? 'Published' : 'Draft'}
                            </span>
                          </div>

                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {news.excerpt}
                          </p>

                          <div className="flex items-center justify-between text-xs text-gray-400">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <i className="fas fa-user"></i>
                                <span>{news.author}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <i className="fas fa-clock"></i>
                                <span>{formatDate(news.date)}</span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <i className="fas fa-eye"></i>
                                <span>{news.views.toLocaleString()}</span>
                              </div>
                              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                {news.category}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {recentNews.length === 0 && (
                  <div className="text-center py-12">
                    <i className="fas fa-newspaper text-4xl text-gray-300 mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada berita</h3>
                    <p className="text-gray-500">Berita yang dipublikasi akan muncul di sini</p>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Activities */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <i className="fas fa-history text-green-600"></i>
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900">Aktivitas Terbaru</h2>
                      <p className="text-sm text-gray-500">Log aktivitas sistem</p>
                    </div>
                  </div>
                  <button className="text-sm text-green-600 hover:text-green-700 font-medium hover:bg-green-50 px-3 py-1 rounded-lg transition-colors">
                    Lihat Log
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {recentActivities.map((activity, index) => (
                    <div key={activity.id} className="flex items-start space-x-4">
                      <div className="flex-shrink-0 relative">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          activity.type === 'create' ? 'bg-green-100' :
                          activity.type === 'update' ? 'bg-blue-100' :
                          activity.type === 'publish' ? 'bg-purple-100' :
                          activity.type === 'delete' ? 'bg-red-100' : 'bg-gray-100'
                        }`}>
                          <i className={`${activity.icon} text-sm ${activity.color}`}></i>
                        </div>
                        {index < recentActivities.length - 1 && (
                          <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-px h-6 bg-gray-200"></div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0 pb-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-sm text-gray-900">
                              <span className="font-medium">{activity.user}</span>
                              {' '}
                              <span className="text-gray-600">{activity.action}</span>
                            </p>
                            <p className="text-sm text-gray-500 mt-1">{activity.description}</p>
                          </div>
                          <span className="text-xs text-gray-400 flex-shrink-0 ml-4">
                            {formatTimeAgo(activity.time)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {recentActivities.length === 0 && (
                  <div className="text-center py-12">
                    <i className="fas fa-history text-4xl text-gray-300 mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada aktivitas</h3>
                    <p className="text-gray-500">Aktivitas sistem akan muncul di sini</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Welcome Modal */}
      <WelcomeModal
        isOpen={showWelcomeModal}
        onClose={() => setShowWelcomeModal(false)}
        userInfo={userInfo}
      />
    </AdminLayout>
  );
};

export default DashboardProfessional;
