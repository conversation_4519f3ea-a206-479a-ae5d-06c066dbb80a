import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminModal from '../../components/admin/AdminModal';
import { Button, Card, Input, Textarea, Select } from '../../components/ui';
import { api } from '../../services/api';
import { useAlert } from '../../contexts/AlertContext';

// Memory cache untuk gallery data
const galleryMemoryCache = new Map();

// Cache untuk gallery data
const getCachedGalleryData = () => {
  try {
    // Check memory cache first
    if (galleryMemoryCache.has('galleryData')) {
      return galleryMemoryCache.get('galleryData');
    }

    // Then check localStorage
    const cached = localStorage.getItem('galleryData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      galleryMemoryCache.set('galleryData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }

  // Return empty array instead of null to always show table
  return [];
};

// Validation schema
const gallerySchema = yup.object().shape({
  title: yup
    .string()
    .required('Judul harus diisi')
    .min(3, 'Judul minimal 3 karakter')
    .max(100, 'Judul maksimal 100 karakter'),
  description: yup
    .string()
    .required('Deskripsi harus diisi')
    .min(10, 'Deskripsi minimal 10 karakter')
    .max(500, 'Deskripsi maksimal 500 karakter'),
  category: yup
    .string()
    .required('Kategori harus dipilih')
    .oneOf(['Kegiatan Rutin', 'Kegiatan Khusus', 'Prestasi', 'Fasilitas', 'Ekstrakurikuler'], 'Kategori tidak valid'),
  featured: yup.boolean(),
  carousel_pinned: yup.boolean()
});

const GalleryManagement = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading: authLoading, redirectToLogin, user } = useAdminAuth();
  const { showSuccess, showError, showDeleteConfirm } = useAlert();

  // Initialize with cached data - always show table immediately
  const cachedImages = getCachedGalleryData();
  const [images, setImages] = useState(cachedImages);
  const [hasTriedFetch, setHasTriedFetch] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 5,
    total: 0
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      redirectToLogin();
    }
  }, [authLoading, isAuthenticated, redirectToLogin]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [existingImageUrl, setExistingImageUrl] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Bulk actions states
  const [selectedImageIds, setSelectedImageIds] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [fileError, setFileError] = useState('');
  
  // Remove loading states - always show content immediately

  // Helper function to get image URL from gallery item
  const getImageUrl = (galleryItem) => {
    if (!galleryItem || !galleryItem.image_url) {
      console.log('No image_url for gallery item:', galleryItem?.title);
      return '/images/placeholder.svg';
    }

    const imageUrl = galleryItem.image_url;
    console.log('Processing gallery image URL:', imageUrl, 'for item:', galleryItem.title);

    // If it's already a full URL from backend (http://localhost:8000/images/gallery/), convert to frontend path
    if (imageUrl.startsWith('http://localhost:8000/images/gallery/')) {
      const filename = imageUrl.split('/').pop();
      const frontendPath = `/images/gallery/${filename}`;
      console.log('Converting backend URL to frontend path:', frontendPath);
      return frontendPath;
    }

    // If it's already a full URL (starts with http), return as is
    if (imageUrl.startsWith('http')) {
      console.log('Using full URL:', imageUrl);
      return imageUrl;
    }

    // If it's /images/gallery path (frontend public format), use directly
    if (imageUrl.startsWith('/images/gallery/')) {
      console.log('Using frontend public gallery path:', imageUrl);
      return imageUrl; // Direct access from frontend public
    }

    // If it's uploads/images/gallery path (old backend format), add base URL
    if (imageUrl.startsWith('/uploads/images/gallery/')) {
      const fullUrl = `http://localhost:8000${imageUrl}?t=${Date.now()}`;
      console.log('Using gallery uploads path:', fullUrl);
      return fullUrl;
    }

    // If it's uploads/images path (news format), add base URL
    if (imageUrl.startsWith('/uploads/images/')) {
      const fullUrl = `http://localhost:8000${imageUrl}?t=${Date.now()}`;
      console.log('Using uploads path:', fullUrl);
      return fullUrl;
    }

    // If it's a relative path starting with /storage/ (old format), add base URL
    if (imageUrl.startsWith('/storage/')) {
      const fullUrl = `http://localhost:8000${imageUrl}`;
      console.log('Using storage path:', fullUrl);
      return fullUrl;
    }

    // If it's a relative path starting with storage/, add base URL with slash
    if (imageUrl.startsWith('storage/')) {
      const fullUrl = `http://localhost:8000/${imageUrl}`;
      console.log('Using relative storage path:', fullUrl);
      return fullUrl;
    }

    // Default fallback - use a working placeholder
    console.log('Using fallback for:', imageUrl);
    return '/images/placeholder.svg'; // Use SVG placeholder
  };

  // Cleanup preview URLs on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset,
    setValue,
    
  } = useForm({
    resolver: yupResolver(gallerySchema),
    defaultValues: {
      title: '',
      description: '',
      image_url: '',
      category: '',
      tags: '[]',
      uploaded_by: '',
      is_active: 1,
      featured: false,
      carousel_pinned: false,
      sort_order: 0
    }
  });

  const categories = [
    'Kegiatan Rutin',
    'Kegiatan Khusus',
    'Prestasi',
    'Fasilitas',
    'Ekstrakurikuler'
  ];

  // Remove image loading handlers - no loading states needed

  // Fetch gallery data from API with optimization
  const fetchGallery = React.useCallback(async (page = 1, filters = {}) => {
    if (!isAuthenticated) return;

    try {
      setError(null);

      // Build query parameters for optimization
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '12', // Increased items per page for better UX
        ...filters,
        timestamp: new Date().getTime() // Cache busting
      });

      const response = await api.get(`/gallery/admin/all?${params.toString()}`);
      console.log('Gallery API Response:', response.data);

      if (response.data.success) {
        // Batch state updates
        const updates = {
          images: response.data.data || [],
          pagination: response.data.pagination || {
            current_page: 1,
            last_page: 1,
            per_page: 12,
            total: 0
          }
        };

        // Validate data
        if (!Array.isArray(updates.images)) {
          console.error('Invalid gallery data format:', updates.images);
          throw new Error('Format data galeri tidak valid');
        }

        console.log('Setting gallery images:', updates.images.length, 'items');
        
        // Use a single render cycle for multiple state updates
        setImages(updates.images);
        setPagination(updates.pagination);
        setHasTriedFetch(true);

        // Save to cache
        galleryMemoryCache.set('galleryData', updates.images);
        localStorage.setItem('galleryData', JSON.stringify(updates.images));

        if (updates.images.length === 0) {
          console.log('No gallery images returned from API');
        }
      } else {
        console.error('API request failed:', response.data.error);
        throw new Error(response.data.error || 'Gagal memuat data galeri');
      }
    } catch (err) {
      console.error('Error fetching gallery:', err);
      const errorMessage = err.message?.includes('Authentication required') || err.message?.includes('401')
        ? 'Sesi login Anda telah berakhir. Silakan login kembali.'
        : 'Terjadi kesalahan saat memuat data. Silakan coba lagi.';
      
      setError(errorMessage);
      setHasTriedFetch(true);

      if (errorMessage.includes('Sesi login')) {
        setTimeout(() => navigate('/admin/login'), 2000);
      }

      // Keep cached data if error occurs
    }
  }, [isAuthenticated, navigate]);

  // Memoize fetchGallery to prevent unnecessary re-renders
  const memoizedFetchGallery = React.useCallback(fetchGallery, [fetchGallery]);

  // Load data on component mount and category changes
  useEffect(() => {
    let mounted = true;

    const loadData = async () => {
      if (!mounted) return;
      
      // Fetch data immediately without loading state
      await memoizedFetchGallery(1, {
        category: selectedCategory === 'all' ? undefined : selectedCategory
      });
    };

    loadData();

    // Cleanup function
    return () => {
      mounted = false;
    };
  }, [selectedCategory, memoizedFetchGallery]);

  const resetForm = () => {
    // Cleanup preview URL if exists
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }
    
    // Reset selected file
    setSelectedFile(null);
    
    // Reset form values
    reset({
      title: '',
      description: '',
      image_url: '',
      category: '',
      tags: '[]',
      uploaded_by: user?.id || 1,
      is_active: 1,
      featured: false,
      carousel_pinned: false,
      sort_order: 0
    });
    setSelectedFile(null);
    setPreviewUrl('');
    setFileError('');
  };

  const openModal = (mode, image = null) => {
    setModalMode(mode);
    setSelectedImage(image);
    setFileError('');

    if (mode === 'edit' && image) {
      // Set form values for editing
      setValue('title', image.title || '');
      setValue('description', image.description || '');
      setValue('category', image.category || '');
      setValue('featured', image.featured || false);
      setValue('carousel_pinned', image.carousel_pinned || false);
      
      // Set preview for existing image
      if (image.image_url) {
        const existingUrl = getImageUrl(image);
        setExistingImageUrl(existingUrl);
        setPreviewUrl(existingUrl);
        console.log('Setting existing image preview:', existingUrl);
      }

      setSelectedFile(null); // Reset selected file since we're editing
      
      console.log('Opening edit modal with data:', {
        id: image.id,
        title: image.title,
        category: image.category,
        image_url: image.image_url
      });
    } else if (mode === 'create') {
      resetForm();
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);

    // Cleanup preview URL only if it's a blob URL (new file)
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl('');
    setExistingImageUrl(null);
    setSelectedFile(null);
    setFileError('');

    resetForm();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validasi tipe file
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setFileError('Tipe file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP');
        return;
      }

      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Validasi ukuran file (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setFileError('Ukuran file terlalu besar. Maksimal 5MB');
        return;
      }

      setSelectedFile(file);
      setFileError(''); // Clear error

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Form submit handler with react-hook-form
  const handleFormSubmit = async (data) => {
    if (!isAuthenticated) return;

    try {

      if (modalMode === 'create') {
        // Validasi file
        if (!selectedFile) {
          setFileError('File gambar harus dipilih');
          return;
        }

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('title', data.title);
        formData.append('description', data.description || '');
        formData.append('category', data.category);
        formData.append('uploaded_by', user?.id?.toString() || '1'); 
        formData.append('is_active', '1');
        formData.append('featured', data.featured ? '1' : '0');
        formData.append('carousel_pinned', data.carousel_pinned ? '1' : '0');
        formData.append('sort_order', '0');
        
        if (selectedFile) {
          formData.append('image', selectedFile);
        }

        const response = await api.post('/gallery', formData, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'multipart/form-data'
          }
        });
          
          if (response.data && (response.data.success || response.data.status === 'success')) {
            showSuccess('Berhasil!', 'Gambar berhasil ditambahkan ke galeri');
            await fetchGallery(pagination.current_page, { category: selectedCategory });
            closeModal();
            // Reset form and selected file
            reset();
            setSelectedFile(null);
            setPreviewUrl(null);
          } else {
            showError('Gagal Upload', response.data?.message || 'Gagal menyimpan gambar');
          }
      } else if (modalMode === 'edit' && selectedImage) {
        // Buat FormData untuk edit
        const formData = new FormData();
        
        // Tambahkan field yang diperlukan
        formData.append('title', data.title);
        formData.append('description', data.description || '');
        formData.append('category', data.category);
        formData.append('featured', data.featured ? '1' : '0');
        formData.append('carousel_pinned', data.carousel_pinned ? '1' : '0');
        formData.append('is_active', '1');
        formData.append('sort_order', '0');
        formData.append('_method', 'PUT'); // Laravel method spoofing

        if (selectedFile) {
          formData.append('image', selectedFile);
        }

        // Handle image changes for edit
        if (selectedFile) {
          // New image uploaded
          console.log('Uploading new gallery image:', selectedFile.name);
          formData.append('image', selectedFile);
        } else {
          // No image changes - preserve existing image
          console.log('Preserving existing gallery image');
        }

        console.log('Updating gallery:', {
          id: selectedImage.id,
          title: data.title,
          category: data.category,
          hasNewImage: !!selectedFile,
          existingImageUrl: selectedImage.image_url
        });

        try {
          const response = await api.upload(`/gallery/${selectedImage.id}`, formData);
          
          if (response.data.success) {
            console.log('Gallery update response:', response.data);
            showSuccess('Berhasil!', 'Gambar berhasil diperbarui');

            // Force refresh gallery data
            console.log('Refreshing gallery data after edit...');
            await fetchGallery(pagination.current_page, { category: selectedCategory });

            // Small delay to ensure state updates
            setTimeout(() => {
              console.log('Gallery data after refresh:', images);
              closeModal();
            }, 200);
          } else {
            throw new Error(response.data.message || 'Gagal mengupdate gambar');
          }
        } catch (error) {
          console.error('Error updating gallery:', error);
          showError('Gagal Update', 'Gagal mengupdate gambar: ' + (error.response?.data?.message || error.message));
        }
      }
    } catch (err) {
      console.error('Error saving gallery:', err);
      if (err.message.includes('Authentication required') || err.message.includes('401')) {
        setError('Sesi login Anda telah berakhir. Silakan login kembali.');
        setTimeout(() => {
          navigate('/admin/login');
        }, 2000);
      } else if (err.message.includes('400')) {
        setError('Data yang dikirim tidak valid. Periksa kembali form Anda.');
      } else if (err.message.includes('500')) {
        setError('Terjadi kesalahan server. Pastikan Anda sudah login sebagai admin.');
      } else {
        setError('Terjadi kesalahan saat menyimpan data');
      }
    }
    // Remove loading state
  };

  const handleDelete = async () => {
    try {
      console.log('Deleting gallery item:', selectedImage.id);

      const response = await api.delete(`/gallery/${selectedImage.id}`);
      console.log('Delete response:', response.data);

      if (response.data.success) {
        showSuccess('Berhasil!', 'Gambar berhasil dihapus dari galeri');
        await fetchGallery(pagination.current_page, { category: selectedCategory });
        closeModal();
      } else {
        showError('Gagal Menghapus', response.data.message || 'Gagal menghapus gambar');
      }
    } catch (err) {
      console.error('Error deleting gallery:', err);
      showError('Error', 'Terjadi kesalahan saat menghapus data: ' + (err.response?.data?.message || err.message));
    }
    // Remove loading state
  };

  const confirmDelete = () => {
    showDeleteConfirm(
      'Hapus Gambar',
      `Apakah Anda yakin ingin menghapus gambar "${selectedImage?.title}"? Tindakan ini tidak dapat dibatalkan.`,
      handleDelete
    );
  };

  const handleToggleCarouselPin = async (imageId) => {
    try {
      const response = await api.post(`/gallery/${imageId}/toggle-carousel-pin`);
      if (response.data.success) {
        await fetchGallery(pagination.current_page, { category: selectedCategory }); // Refresh data with current page

        // Show success message with better UX
        const isPinned = response.data.data.carousel_pinned;
        const message = isPinned
          ? `✅ Gambar berhasil di-pin ke carousel! Sekarang akan muncul di halaman utama.`
          : `📌 Gambar berhasil di-unpin dari carousel.`;

        // Create toast notification instead of alert
        showToast(message, 'success');

        // Trigger carousel refresh di Home page (jika ada)
        if (window.refreshCarousel) {
          window.refreshCarousel();
        }
      } else {
        setError('Gagal mengubah status pin carousel');
      }
    } catch (err) {
      console.error('Error toggling carousel pin:', err);
      setError('Terjadi kesalahan saat mengubah status pin');
    }
  };

  // Toast notification function
  const showToast = (message, type = 'info') => {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transform transition-all duration-300 ${
      type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;

    // Add to DOM
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 3000);
  };

  // Auto reload when no data is found
  useEffect(() => {
    let reloadTimer;
    if (images.length === 0 && hasTriedFetch) {
      reloadTimer = setTimeout(() => {
        console.log('Auto-reloading data...');
        fetchGallery(1, { category: selectedCategory });
      }, 5000); // Reload setiap 5 detik jika tidak ada data
    }
    return () => {
      if (reloadTimer) clearTimeout(reloadTimer);
    };
  }, [images.length, hasTriedFetch, selectedCategory, fetchGallery]);

  // Memoize filtered images to prevent unnecessary recalculation
  const filteredImages = React.useMemo(() => {
    if (selectedCategory === 'all') return images;
    return images.filter(img => img.category === selectedCategory);
  }, [images, selectedCategory]);

  // Remove loading state component - always show content immediately

  // Bulk actions functions
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedImageIds([]);
      setSelectAll(false);
    } else {
      setSelectedImageIds(filteredImages.map(item => item.id));
      setSelectAll(true);
    }
  };

  const handleSelectImage = (imageId) => {
    if (selectedImageIds.includes(imageId)) {
      const newSelected = selectedImageIds.filter(id => id !== imageId);
      setSelectedImageIds(newSelected);
      setSelectAll(false);
    } else {
      const newSelected = [...selectedImageIds, imageId];
      setSelectedImageIds(newSelected);
      setSelectAll(newSelected.length === filteredImages.length);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedImageIds.length === 0) return;

    if (!confirm(`Apakah Anda yakin ingin menghapus ${selectedImageIds.length} gambar yang dipilih?`)) {
      return;
    }

    try {
      const deletePromises = selectedImageIds.map(id =>
        api.delete(`/gallery/${id}`)
      );

      await Promise.all(deletePromises);
      await fetchGallery(pagination.current_page, { category: selectedCategory }); // Refresh data with current page
      setSelectedImageIds([]);
      setSelectAll(false);
      alert(`${selectedImageIds.length} gambar berhasil dihapus!`);
    } catch (err) {
      console.error('Error bulk deleting images:', err);
      alert('Terjadi kesalahan saat menghapus gambar: ' + err.message);
    }
  };

  // Reset selection when category changes
  useEffect(() => {
    setSelectedImageIds([]);
    setSelectAll(false);
  }, [selectedCategory]);

  // Show loading while checking auth or if not authenticated
  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {authLoading ? 'Checking authentication...' : 'Redirecting to login...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kelola Galeri</h1>
            <p className="text-gray-600 mt-2">
              Kelola semua gambar dan foto kegiatan sekolah
              {pagination.total > 0 && (
                <span className="text-blue-600 font-medium">
                  ({pagination.total} total, {pagination.per_page} per halaman)
                </span>
              )}
            </p>
          </div>
          <Button
            onClick={() => openModal('create')}
            variant="primary"
            size="lg"
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            }
          >
            Upload Gambar
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Terjadi Kesalahan</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                  {error.includes('login') && (
                    <div className="mt-3">
                      <Button
                        onClick={() => navigate('/admin/login')}
                        variant="outline"
                        size="sm"
                        className="border-red-300 text-red-700 hover:bg-red-50"
                      >
                        Login Sekarang
                      </Button>
                    </div>
                  )}
                </div>
                <div className="ml-auto">
                  <button
                    onClick={() => setError(null)}
                    className="text-red-400 hover:text-red-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filter */}
        <div className="mb-6">
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex gap-2 min-w-max pb-2 min-[900px]:flex-wrap min-[900px]:min-w-0">
              <div>
                <Button
                  onClick={() => setSelectedCategory('all')}
                  variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                  size="sm"
                  className="whitespace-nowrap"
                >
                  Semua ({images.length})
                </Button>
              </div>
              {categories.map((category) => {
                const count = images.filter(img => img.category === category).length;
                return (
                  <div key={category}>
                    <Button
                      onClick={() => setSelectedCategory(category)}
                      variant={selectedCategory === category ? 'primary' : 'outline'}
                      size="sm"
                      className="whitespace-nowrap"
                    >
                      {category} ({count})
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedImageIds.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-yellow-800 font-medium">
                  {selectedImageIds.length} gambar dipilih
                </span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleBulkDelete}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition duration-300 flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  <span>Hapus {selectedImageIds.length} Item</span>
                </button>
                <button
                  onClick={() => {
                    setSelectedImageIds([]);
                    setSelectAll(false);
                  }}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg font-medium transition duration-300"
                >
                  Batal
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Gallery Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Checkbox Column */}
                  <th className="px-2 sm:px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8 sm:w-12">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gambar
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kategori
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Carousel
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Upload By
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredImages.length === 0 && hasTriedFetch ? (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada Galeri</h3>
                        <p className="text-gray-500 mb-4">Mulai dengan menambahkan gambar pertama Anda</p>
                        <div className="mt-4">
                          <Button
                            onClick={() => openModal('create')}
                            variant="primary"
                            size="md"
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            }
                          >
                            Upload Gambar Baru
                          </Button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredImages.map((image) => {
                    const isSelected = selectedImageIds.includes(image.id);
                    return (
                    <tr
                      key={image.id}
                      className={`transition-colors duration-200 ${
                        isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'
                      }`}
                    >
                      {/* Checkbox Column */}
                      <td className="px-2 sm:px-3 py-4">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleSelectImage(image.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-16 w-16 relative">
                          <img
                            src={getImageUrl(image)}
                            alt={image.title}
                            className="h-16 w-16 rounded-lg object-cover transition-opacity duration-300 relative z-10"
                            onError={(e) => {
                              console.log('Gallery image load error for:', image.title, 'URL:', e.target.src);
                              e.target.onerror = null;
                              e.target.src = '/images/placeholder.svg';
                            }}
                            onLoad={(e) => {
                              console.log('Gallery image loaded successfully:', e.target.src);
                            }}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 line-clamp-2">
                            {image.title}
                          </div>
                          <div className="text-sm text-gray-500 mt-1 line-clamp-1">
                            {image.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                        {typeof image.category === 'object' ? image.category?.name : image.category || 'Umum'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {image.featured || image.is_featured ? (
                        <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          Featured
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          Normal
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {image.carousel_pinned ? (
                          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            Pinned
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                            Not Pinned
                          </span>
                        )}
                        <Button
                          onClick={() => handleToggleCarouselPin(image.id)}
                          variant="ghost"
                          size="sm"
                          className={`${image.carousel_pinned ? 'text-red-600 hover:text-red-900 hover:bg-red-50' : 'text-green-600 hover:text-green-900 hover:bg-green-50'}`}
                          icon={
                            image.carousel_pinned ? (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                              </svg>
                            )
                          }
                          title={image.carousel_pinned ? 'Unpin dari Carousel' : 'Pin ke Carousel'}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {typeof image.uploadedBy === 'object' ? image.uploadedBy?.name : image.uploadedBy ||
                       typeof image.uploaded_by === 'object' ? image.uploaded_by?.name : image.uploaded_by || 'Admin'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {image.created_at ? new Date(image.created_at).toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-1">
                        <Button
                          onClick={() => openModal('view', image)}
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-900 hover:bg-blue-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          }
                        />
                        <Button
                          onClick={() => openModal('edit', image)}
                          variant="ghost"
                          size="sm"
                          className="text-green-600 hover:text-green-900 hover:bg-green-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          }
                        />
                        <Button
                          onClick={() => openModal('delete', image)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-900 hover:bg-red-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          }
                        />
                      </div>
                    </td>
                  </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination - Shows when more than 5 items */}
          {pagination.last_page > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  onClick={() => fetchGallery(pagination.current_page - 1, { category: selectedCategory })}
                  disabled={pagination.current_page <= 1}
                  variant="outline"
                  size="sm"
                >
                  ← Previous
                </Button>
                <span className="text-sm text-gray-700">
                  Page {pagination.current_page} of {pagination.last_page}
                </span>
                <Button
                  onClick={() => fetchGallery(pagination.current_page + 1, { category: selectedCategory })}
                  disabled={pagination.current_page >= pagination.last_page}
                  variant="outline"
                  size="sm"
                >
                  Next →
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{pagination.from || 1}</span> to{' '}
                    <span className="font-medium">{pagination.to || pagination.per_page}</span> of{' '}
                    <span className="font-medium">{pagination.total}</span> results
                    <span className="text-gray-500 ml-2">(5 items per page)</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <Button
                      onClick={() => fetchGallery(pagination.current_page - 1, { category: selectedCategory })}
                      disabled={pagination.current_page <= 1}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      ← Previous
                    </Button>

                    {/* Page Numbers */}
                    {Array.from({ length: pagination.last_page }, (_, i) => i + 1).map((pageNum) => (
                      <Button
                        key={pageNum}
                        onClick={() => fetchGallery(pageNum, { category: selectedCategory })}
                        variant={pageNum === pagination.current_page ? "primary" : "outline"}
                        size="sm"
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pageNum === pagination.current_page
                            ? 'z-10 bg-blue-600 border-blue-600 text-white'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </Button>
                    ))}

                    <Button
                      onClick={() => fetchGallery(pagination.current_page + 1, { category: selectedCategory })}
                      disabled={pagination.current_page >= pagination.last_page}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next →
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Modal */}
        <AdminModal
          isOpen={isModalOpen && (modalMode === 'create' || modalMode === 'edit')}
          onClose={closeModal}
          title={modalMode === 'create' ? 'Upload Gambar Baru' : 'Edit Gambar'}
          size="xl"
        >
          <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Judul"
                placeholder="Masukkan judul gambar"
                required
                error={formErrors.title?.message}
                {...register('title')}
              />

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori <span className="text-red-500">*</span>
                </label>
                <select
                  id="category"
                  className={`w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    formErrors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('category')}
                >
                  <option value="">Pilih Kategori</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {formErrors.category && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="image-upload" className="block text-sm font-medium text-gray-700 mb-2">
                Upload Gambar {modalMode === 'create' ? '*' : '(Opsional)'}
              </label>
              <div className="space-y-4">
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    fileError ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {/* Image Preview */}
                {(previewUrl || (selectedImage && !selectedFile)) && (
                  <div className="mt-4">
                    <div className="relative group">
                      <img
                        src={previewUrl || selectedImage?.image_url}
                        alt="Preview"
                        className="w-full max-h-[300px] object-contain rounded-lg border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg"></div>
                    </div>
                  </div>
                )}
                {fileError && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {fileError}
                  </p>
                )}

                {/* Preview */}
                {previewUrl && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">Preview:</label>
                      {modalMode === 'edit' && (
                        <span className="text-xs text-gray-500">
                          {selectedFile ? 'Gambar baru (belum disimpan)' : 'Gambar saat ini'}
                        </span>
                      )}
                    </div>
                    <div className="relative inline-block">
                      <img
                        src={previewUrl}
                        alt="Preview"
                        className="max-w-full h-32 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          // If it's a new file (blob URL), revoke it
                          if (previewUrl && previewUrl.startsWith('blob:')) {
                            URL.revokeObjectURL(previewUrl);
                          }

                          setSelectedFile(null);

                          if (modalMode === 'edit' && existingImageUrl) {
                            // Restore existing image preview
                            setPreviewUrl(existingImageUrl);
                          } else {
                            // Clear preview completely
                            setPreviewUrl('');
                          }
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                        title={modalMode === 'edit' && existingImageUrl ? 'Hapus gambar baru (kembali ke gambar asli)' : 'Hapus preview'}
                      >
                        ×
                      </button>
                    </div>
                  </div>
                )}

                <p className="text-sm text-gray-500">
                  Format yang didukung: JPG, PNG, GIF, WebP. Maksimal 5MB.
                  {modalMode === 'edit' && (
                    <span className="block mt-1 text-blue-600">
                      💡 Kosongkan jika tidak ingin mengubah gambar
                    </span>
                  )}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


            </div>

            <Textarea
              label="Deskripsi"
              placeholder="Deskripsi gambar"
              rows={4}
              required
              error={formErrors.description?.message}
              {...register('description')}
            />

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  {...register('featured')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                  Jadikan gambar featured
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="carousel_pinned"
                  {...register('carousel_pinned')}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="carousel_pinned" className="ml-2 block text-sm text-gray-900">
                  Pin ke Carousel (tampil di halaman utama)
                </label>
              </div>
            </div>

            <AdminModal.Footer>
              <Button
                type="button"
                onClick={closeModal}
                variant="outline"
                disabled={isSubmitting}
              >
                Batal
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {modalMode === 'create' ? 'Upload' : 'Update'}
              </Button>
            </AdminModal.Footer>
          </form>
        </AdminModal>

        {/* View Modal - Enhanced Card Design */}
        <AdminModal
          isOpen={isModalOpen && modalMode === 'view'}
          onClose={closeModal}
          title=""
          size="xl"
          className="p-0"
        >
          {selectedImage && (
            <div className="bg-white rounded-lg overflow-hidden">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-white">Detail Gambar</h2>
                  <button
                    onClick={closeModal}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* Image Card */}
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  <div className="relative group">
                    <img
                      src={selectedImage.url || selectedImage.image_url || selectedImage.path || '/images/gallery/gallery_1_1753243029.jpg'}
                      alt={selectedImage.title}
                      className="w-full h-auto rounded-lg shadow-lg transition-transform duration-300 group-hover:scale-105"
                      style={{ maxHeight: '500px', objectFit: 'contain' }}
                      onError={(e) => {
                        e.target.src = '/images/placeholder.svg';
                      }}
                    />

                    {/* Image overlay with info */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Info Card */}
                  <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">Informasi Gambar</h3>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Judul</label>
                        <p className="text-gray-900 font-medium">{selectedImage.title}</p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Kategori</label>
                          <div className="flex items-center mt-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {selectedImage.category}
                            </span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-gray-500">Status</label>
                          <div className="flex items-center mt-1 space-x-2">
                            {selectedImage.featured && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                ⭐ Featured
                              </span>
                            )}
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              ✓ Aktif
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Details Card */}
                  <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">Detail</h3>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Deskripsi</label>
                        <p className="text-gray-700 mt-1 leading-relaxed">
                          {selectedImage.description || 'Tidak ada deskripsi'}
                        </p>
                      </div>

                      <div className="grid grid-cols-1 gap-3 pt-3 border-t border-gray-100">
                        <div className="flex items-center text-sm text-gray-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          <span>Diupload oleh: {selectedImage.uploadedBy || 'Admin'}</span>
                        </div>

                        <div className="flex items-center text-sm text-gray-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V8a1 1 0 011-1h3z" />
                          </svg>
                          <span>Tanggal: {selectedImage.created_at ? new Date(selectedImage.created_at).toLocaleDateString('id-ID', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Tidak diketahui'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <Button
                    onClick={() => {
                      closeModal();
                      openModal('edit', selectedImage);
                    }}
                    variant="outline"
                    className="flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>Edit</span>
                  </Button>

                  <Button
                    onClick={closeModal}
                    variant="primary"
                    className="flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Selesai</span>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </AdminModal>

        {/* Delete Modal */}
        <AdminModal
          isOpen={isModalOpen && modalMode === 'delete'}
          onClose={closeModal}
          title="Hapus Gambar"
          size="sm"
        >
          {selectedImage && (
            <div>
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600 text-center">
                  Apakah Anda yakin ingin menghapus gambar ini? Tindakan ini tidak dapat dibatalkan.
                </p>
                <p className="text-sm font-medium text-gray-900 text-center mt-2">
                  "{selectedImage.title}"
                </p>
              </div>
              
              <AdminModal.Footer>
                <Button
                  onClick={closeModal}
                  variant="outline"
                >
                  Batal
                </Button>
                <Button
                  onClick={confirmDelete}
                  variant="danger"
                >
                  Hapus
                </Button>
              </AdminModal.Footer>
            </div>
          )}
        </AdminModal>
      </div>
    </AdminLayout>
  );
};

export default GalleryManagement;
