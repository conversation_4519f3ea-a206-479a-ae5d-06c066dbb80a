import { useState, useEffect, useCallback } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import AdminModal from '../../components/admin/AdminModal';
import ErrorBoundary from '../../components/ui/ErrorBoundary';
import { api } from '../../services/api';
import { useAdminAuth } from '../../hooks/useAdminAuth';

// Memory cache untuk news data
const newsMemoryCache = new Map();

// Cache untuk news data
const getCachedNewsData = () => {
  try {
    // Check memory cache first
    if (newsMemoryCache.has('newsData')) {
      return newsMemoryCache.get('newsData');
    }

    // Then check localStorage
    const cached = localStorage.getItem('newsData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      newsMemoryCache.set('newsData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }

  // Return empty array instead of null to always show table
  return [];
};

const NewsManagement = () => {
  const { isAuthenticated, isLoading: authLoading, redirectToLogin, user } = useAdminAuth();

  // Initialize with cached data - always show table immediately
  const cachedNews = getCachedNewsData();
  const [news, setNews] = useState(cachedNews);
  const [hasTriedFetch, setHasTriedFetch] = useState(false);

  // Bulk actions states
  const [selectedNewsIds, setSelectedNewsIds] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      redirectToLogin();
    }
  }, [authLoading, isAuthenticated, redirectToLogin]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view', 'delete'
  const [selectedNews, setSelectedNews] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    content: '',
    category: '',
    author: '',
    status: 'draft',
    image: null,
    imageUrl: ''
  });
  const [imagePreview, setImagePreview] = useState(null);
  const [errors, setErrors] = useState({});
  const [refreshKey, setRefreshKey] = useState(0); // Force re-render key

  const categories = ['Prestasi', 'Akademik', 'Kegiatan', 'Pengumuman', 'Fasilitas'];
  
  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file
    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        image: 'Format file harus JPG, PNG, GIF, atau WebP'
      }));
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setErrors(prev => ({
        ...prev,
        image: 'Ukuran file maksimal 5MB'
      }));
      return;
    }

    try {
      // Show preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);

      // Set file to form data
      setFormData(prev => ({
        ...prev,
        image: file
      }));

    } catch (error) {
      console.error('Error handling image:', error);
      setErrors(prev => ({
        ...prev,
        image: 'Gagal memproses gambar'
      }));
    }
  };
  


  const getImageUrl = (newsItem) => {
    if (!newsItem || !newsItem.image_url) {
      return '/images/placeholder.svg';
    }

    const imageUrl = newsItem.image_url;
    console.log('Processing news image URL:', imageUrl, 'for item:', newsItem.title);

    // If it's already a full URL from backend (http://localhost:8000/images/news/), convert to frontend path
    if (imageUrl.startsWith('http://localhost:8000/images/news/')) {
      const filename = imageUrl.split('/').pop();
      const frontendPath = `/images/news/${filename}`;
      console.log('Converting backend URL to frontend path:', frontendPath);
      return frontendPath;
    }

    // If it's already a full URL (starts with http), return as is
    if (imageUrl.startsWith('http')) {
      console.log('Using full URL:', imageUrl);
      return imageUrl;
    }

    // If it's /images/news path (frontend public format), use directly
    if (imageUrl.startsWith('/images/news/')) {
      console.log('Using frontend public news path:', imageUrl);
      return imageUrl; // Direct access from frontend public
    }

    // If it's uploads/images/news path (old backend format), add base URL
    if (imageUrl.startsWith('/uploads/images/news/')) {
      const fullUrl = `http://localhost:8000${imageUrl}?t=${Date.now()}`;
      console.log('Using news uploads path:', fullUrl);
      return fullUrl;
    }

    // If it's uploads/images path (old format), add base URL
    if (imageUrl.startsWith('/uploads/images/')) {
      const fullUrl = `http://localhost:8000${imageUrl}?t=${Date.now()}`;
      console.log('Using uploads path:', fullUrl);
      return fullUrl;
    }

    // If it's a relative path starting with /storage/ (old format), add base URL
    if (imageUrl.startsWith('/storage/')) {
      const fullUrl = `http://localhost:8000${imageUrl}`;
      console.log('Using storage path:', fullUrl);
      return fullUrl;
    }

    // Default fallback - use a working placeholder
    console.log('Using fallback for:', imageUrl);
    return '/images/placeholder.svg';
  };

  // Fetch news data from API in background
  const fetchNews = useCallback(async () => {
    try {
      const response = await api.get('/news/admin/all');
      if (response.data.success) {
        const newsData = response.data.data || [];
        setNews(newsData);
        setHasTriedFetch(true);

        // Save to cache
        newsMemoryCache.set('newsData', newsData);
        localStorage.setItem('newsData', JSON.stringify(newsData));
      } else {
        console.log('Failed to fetch news data');
        setHasTriedFetch(true);
      }
    } catch (err) {
      console.error('Error fetching news:', err);
      setHasTriedFetch(true);
      // Keep cached data, don't show alert or clear data
    }
  }, []);

  // Load data on component mount
  useEffect(() => {
    fetchNews();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Pagination calculations
  const totalPages = Math.ceil(news.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedNews = news.slice(startIndex, endIndex);

  // Bulk actions functions
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedNewsIds([]);
      setSelectAll(false);
    } else {
      setSelectedNewsIds(paginatedNews.map(item => item.id));
      setSelectAll(true);
    }
  };

  const handleSelectNews = (newsId) => {
    if (selectedNewsIds.includes(newsId)) {
      const newSelected = selectedNewsIds.filter(id => id !== newsId);
      setSelectedNewsIds(newSelected);
      setSelectAll(false);
    } else {
      const newSelected = [...selectedNewsIds, newsId];
      setSelectedNewsIds(newSelected);
      setSelectAll(newSelected.length === paginatedNews.length);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNewsIds.length === 0) return;

    // Konfirmasi dengan alert biasa
    const confirmDelete = confirm(`Apakah Anda yakin ingin menghapus ${selectedNewsIds.length} berita yang dipilih?`);
    if (!confirmDelete) return;

    try {
      const deletePromises = selectedNewsIds.map(newsId =>
        api.delete(`/news/${newsId}`)
      );

      await Promise.all(deletePromises);
      await fetchNews();
      setSelectedNewsIds([]);
      setSelectAll(false);
      alert(`${selectedNewsIds.length} berita berhasil dihapus!`);
    } catch (err) {
      console.error('Error bulk deleting news:', err);
      alert('Terjadi kesalahan saat menghapus berita: ' + err.message);
    }
    // Remove loading state
  };

  // Reset pagination when data changes
  useEffect(() => {
    setCurrentPage(1);
    setSelectedNewsIds([]);
    setSelectAll(false);
  }, [news.length]);

  const resetForm = () => {
    setFormData({
      title: '',
      excerpt: '',
      content: '',
      category: '',
      author: user?.name || 'Admin', // Default ke nama user yang login atau "Admin"
      status: 'draft',
      image: null
    });
    setImagePreview(null);
    setErrors({});
  };

  const openModal = (mode, newsItem = null) => {
    setModalMode(mode);
    setSelectedNews(newsItem);

    if (mode === 'edit' && newsItem) {
      // Handle author field - it could be a string or an object with name property
      let authorName = '';
      if (typeof newsItem.author === 'string') {
        authorName = newsItem.author;
      } else if (newsItem.author && newsItem.author.name) {
        authorName = newsItem.author.name;
      } else if (newsItem.author_name) {
        authorName = newsItem.author_name;
      }

      // Jika author kosong, gunakan nama user yang login atau default ke "Admin"
      if (!authorName) {
        authorName = user?.name || 'Admin';
      }

      // Simpan path gambar lama untuk fallback preview (tidak perlu variabel imagePath)

      // Tentukan gambar lama: gunakan image_url dari backend (storage URL)
      let oldImage = getImageUrl(newsItem);

      setFormData({
        title: newsItem.title || '',
        excerpt: newsItem.excerpt || '',
        content: newsItem.content || '',
        category: newsItem.category || '',
        author: authorName,
        status: newsItem.status || 'draft',
        image: 'preserve', // Special value to indicate preserve existing image
        oldImage: oldImage,
        imageUrl: newsItem.image_url // Keep original URL for reference
      });
      setImagePreview(oldImage);
    } else if (mode === 'create') {
      resetForm();
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedNews(null);
    resetForm();
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value || '' // Ensure value is always a string
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Image handler has been moved and consolidated with the other handleImageChange function above

  // Helper function to safely check if a string field is empty
  const isFieldEmpty = (field) => {
    return !field || typeof field !== 'string' || !field.trim();
  };

  const validateForm = () => {
    const newErrors = {};

    if (isFieldEmpty(formData.title)) newErrors.title = 'Judul harus diisi';
    if (isFieldEmpty(formData.excerpt)) newErrors.excerpt = 'Ringkasan harus diisi';
    if (isFieldEmpty(formData.content)) newErrors.content = 'Konten harus diisi';
    if (!formData.category) newErrors.category = 'Kategori harus dipilih';
    // Author tidak wajib, akan default ke nama user atau "Admin"

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      // Optimistic UI - simpan mode untuk pesan sukses
      const isCreate = modalMode === 'create';

      // Prepare news data
      const submitData = new FormData();
      submitData.append('title', formData.title);
      submitData.append('excerpt', formData.excerpt);
      submitData.append('content', formData.content);
      submitData.append('category', formData.category);
      submitData.append('author', formData.author || user?.name || 'Admin');
      submitData.append('status', formData.status);
      submitData.append('published', formData.status === 'published' ? '1' : '0');

      if (modalMode === 'create') {
        // Handle image upload for create
        if (formData.image && formData.image instanceof File) {
          submitData.append('image', formData.image);
        }

        await api.post('/news', submitData);
      } else if (modalMode === 'edit') {
        // Handle image changes for edit
        if (formData.image && formData.image instanceof File) {
          // New image uploaded
          submitData.append('image', formData.image);
        } else if (formData.image === null) {
          // Image was deleted
          submitData.append('delete_image', '1');
        } else if (formData.image === 'preserve') {
          // No image changes - preserve existing image (don't send anything)
        } else {
          // Fallback - preserve existing image
        }

        submitData.append('_method', 'PUT'); // Laravel requires this for PUT requests with FormData
        await api.post(`/news/${selectedNews.id}`, submitData);
      }

      // Force refresh data and wait for it to complete
      await fetchNews();

      // Force re-render of table
      setRefreshKey(prev => prev + 1);

      // Clear any cached image previews
      setImagePreview(null);

      // Tutup modal dan tampilkan sukses langsung
      closeModal();
      alert(isCreate ? 'Berita berhasil dibuat!' : 'Berita berhasil diperbarui!');
    } catch (err) {
      console.error('Error saving news:', err);
      alert('Gagal menyimpan: ' + (err.response?.data?.error || err.message || 'Terjadi kesalahan saat menyimpan data'));
    }
    // Remove loading state
  };

  const handleDelete = async (newsItem) => {
    // Validasi newsItem
    if (!newsItem || !newsItem.id) {
      alert('Data berita tidak valid');
      return;
    }

    // Konfirmasi dengan alert biasa
    const confirmDelete = confirm(`Apakah Anda yakin ingin menghapus berita "${newsItem.title}"?`);
    if (!confirmDelete) return;

    try {
      console.log('Deleting news with ID:', newsItem.id);

      const response = await api.delete(`/news/${newsItem.id}`);

      if (response.data.success) {
        alert('Berita berhasil dihapus!');
        await fetchNews();
      } else {
        alert('Gagal menghapus berita: ' + (response.data.message || response.data.error || 'Terjadi kesalahan'));
      }
    } catch (err) {
      console.error('Delete error:', err);

      if (err.response?.status === 404) {
        alert('Berita tidak ditemukan. Mungkin sudah dihapus sebelumnya.');
        await fetchNews(); // Refresh data
      } else if (err.response?.status === 500) {
        alert('Server error: ' + (err.response?.data?.error || err.response?.data?.message || 'Terjadi kesalahan server'));
      } else {
        alert('Terjadi kesalahan saat menghapus data: ' + (err.response?.data?.error || err.response?.data?.message || err.message));
      }
    }
    // Remove loading state
  };



  const getStatusBadge = (status) => {
    const statusConfig = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-gray-100 text-gray-800'
    };
    
    return statusConfig[status] || statusConfig.draft;
  };

  // Only redirect if not authenticated, no loading screen
  if (!authLoading && !isAuthenticated) {
    return null; // Let redirect happen immediately
  }

  return (
    <ErrorBoundary>
      <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          {/* Desktop Header (>= 900px) */}
          <div className="hidden min-[900px]:flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Kelola Berita</h1>
              <p className="text-gray-600 mt-2">
                Kelola semua berita dan artikel sekolah
                {news.length > 0 && (
                  <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {news.length} berita
                  </span>
                )}
                {selectedNewsIds.length > 0 && (
                  <span className="ml-2 text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    {selectedNewsIds.length} dipilih
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Desktop Bulk Actions */}
              {selectedNewsIds.length > 0 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleBulkDelete}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium flex items-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Hapus {selectedNewsIds.length} Item
                  </button>
                  <button
                    onClick={() => {
                      setSelectedNewsIds([]);
                      setSelectAll(false);
                    }}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg font-medium"
                  >
                    Batal
                  </button>
                </div>
              )}
              <button
                onClick={() => openModal('create')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Tambah Berita
              </button>
            </div>
          </div>

          {/* Mobile Header (< 900px) */}
          <div className="min-[900px]:hidden">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Kelola Berita</h1>
                <p className="text-gray-600 mt-1 text-sm">
                  {news.length} berita
                  {selectedNewsIds.length > 0 && (
                    <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                      {selectedNewsIds.length} dipilih
                    </span>
                  )}
                </p>
              </div>
              <button
                onClick={() => openModal('create')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium flex items-center text-sm"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Tambah
              </button>
            </div>

            {/* Mobile Bulk Actions Bar */}
            {selectedNewsIds.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="text-sm font-medium text-red-800">
                      {selectedNewsIds.length} item dipilih
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleBulkDelete}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded text-sm font-medium"
                    >
                      Hapus
                    </button>
                    <button
                      onClick={() => {
                        setSelectedNewsIds([]);
                        setSelectAll(false);
                      }}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1.5 rounded text-sm font-medium"
                    >
                      Batal
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>



        {/* News Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table key={refreshKey} className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Checkbox Column */}
                  <th className="px-2 sm:px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8 sm:w-12">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  {/* No Column - Hidden on mobile */}
                  <th className="hidden sm:table-cell px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                    No
                  </th>
                  {/* Image Column */}
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                    Gambar
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Berita
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kategori
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Views
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {news.length === 0 && hasTriedFetch ? (
                  <tr>
                    <td colSpan="9" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Belum ada Berita</h3>
                        <p className="text-gray-500 mb-4">Mulai dengan menambahkan berita pertama Anda</p>
                        <button
                          onClick={() => openModal('create')}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium flex items-center"
                        >
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                          Tambah Berita
                        </button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  paginatedNews.map((item, index) => {
                    const isSelected = selectedNewsIds.includes(item.id);
                    const rowNumber = (currentPage - 1) * itemsPerPage + index + 1;

                    return (
                      <tr key={item.id} className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}>
                        {/* Checkbox Column */}
                        <td className="px-2 sm:px-3 py-4">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handleSelectNews(item.id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </td>
                        {/* No Column - Hidden on mobile */}
                        <td className="hidden sm:table-cell px-3 py-4 text-sm text-gray-900 font-medium">
                          {rowNumber}
                        </td>
                        {/* Image Column: simple img without loading animation */}
                        <td className="px-3 py-4">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden flex items-center justify-center">
                            <img
                              src={getImageUrl(item)}
                              alt={item.title}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.target.src = '/images/placeholder-news.jpg';
                                console.error('❌ News image error:', item.title, '|', item.image_url);
                              }}
                            />
                          </div>
                        </td>
                        <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900 line-clamp-2">
                          {item.title}
                        </div>
                        <div className="text-sm text-gray-500 mt-1 line-clamp-1">
                          {item.excerpt || (typeof item.content === 'string' ? item.content.substring(0, 100) + '...' : 'Tidak ada ringkasan')}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Oleh: {typeof item.author === 'object' ? item.author?.name : item.author ||
                                 typeof item.author_name === 'object' ? item.author_name?.name : item.author_name ||
                                 'Admin'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                        {typeof item.category === 'object' ? item.category?.name : item.category || 'Umum'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(item.status || 'draft')}`}>
                        {item.status === 'published' ? 'Published' :
                         item.status === 'archived' ? 'Archived' : 'Draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.views || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.published_at ? new Date(item.published_at).toLocaleDateString('id-ID') :
                       item.created_at ? new Date(item.created_at).toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => openModal('view', item)}
                          className="text-blue-600 hover:text-blue-900 p-1"
                          title="Lihat"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => openModal('edit', item)}
                          className="text-green-600 hover:text-green-900 p-1"
                          title="Edit"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => {
                            console.log('Delete button clicked for item:', item);
                            handleDelete(item);
                          }}
                          className="text-red-600 hover:text-red-900 p-1"
                          title={`Hapus (ID: ${item.id})`}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {news.length > 0 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              {/* Mobile Pagination */}
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md ${
                    currentPage === 1
                      ? 'border-gray-300 text-gray-500 bg-gray-50 cursor-not-allowed'
                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <span className="text-sm text-gray-700 flex items-center">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md ${
                    currentPage === totalPages
                      ? 'border-gray-300 text-gray-500 bg-gray-50 cursor-not-allowed'
                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>

              {/* Desktop Pagination */}
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">{startIndex + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(endIndex, news.length)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{news.length}</span>
                    {' '}results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {/* Previous Button */}
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium ${
                        currentPage === 1
                          ? 'border-gray-300 text-gray-500 bg-gray-50 cursor-not-allowed'
                          : 'border-gray-300 text-gray-500 bg-white hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>

                    {/* Page Numbers */}
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          page === currentPage
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}

                    {/* Next Button */}
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium ${
                        currentPage === totalPages
                          ? 'border-gray-300 text-gray-500 bg-gray-50 cursor-not-allowed'
                          : 'border-gray-300 text-gray-500 bg-white hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Modals */}
        {/* Create/Edit Modal */}
        <AdminModal
          isOpen={isModalOpen && (modalMode === 'create' || modalMode === 'edit')}
          onClose={closeModal}
          title={modalMode === 'create' ? 'Tambah Berita Baru' : 'Edit Berita'}
          size="xl"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Title */}
            <div className="space-y-2">
              <label htmlFor="news-title" className="block text-sm font-semibold text-gray-800">
                Judul Berita *
              </label>
              <input
                type="text"
                id="news-title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.title ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                placeholder="Masukkan judul berita yang menarik..."
                autoComplete="off"
              />
              {errors.title && <p className="text-red-600 text-sm mt-1 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.title}
              </p>}
            </div>

            {/* Category & Author */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label htmlFor="news-category" className="block text-sm font-semibold text-gray-800">
                  Kategori *
                </label>
                <select
                  id="news-category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.category ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <option value="">Pilih Kategori</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.category && <p className="text-red-600 text-sm mt-1 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {errors.category}
                </p>}
              </div>

              <div className="space-y-2">
                <label htmlFor="news-author" className="block text-sm font-semibold text-gray-800">
                  Penulis <span className="text-gray-500 text-xs font-normal">(opsional)</span>
                </label>
                <input
                type="text"
                id="news-author"
                name="author"
                value={formData.author}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border-2 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                    errors.author ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                  }`}
                placeholder={`Nama penulis (default: ${user?.name || 'Admin'})`}
                autoComplete="off"
              />
              <p className="text-gray-500 text-xs mt-1">
                Kosongkan untuk menggunakan nama Anda ({user?.name || 'Admin'}) sebagai penulis
              </p>
              {errors.author && <p className="text-red-600 text-sm mt-1 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.author}
              </p>}
              </div>
            </div>

            {/* Excerpt */}
            <div>
              <label htmlFor="news-excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                Ringkasan *
              </label>
              <textarea
                id="news-excerpt"
                name="excerpt"
                value={formData.excerpt}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.excerpt ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ringkasan singkat berita"
                autoComplete="off"
              />
              {errors.excerpt && <p className="text-red-600 text-sm mt-1">{errors.excerpt}</p>}
            </div>

            {/* Content */}
            <div>
              <label htmlFor="news-content" className="block text-sm font-medium text-gray-700 mb-2">
                Konten *
              </label>
              <textarea
                id="news-content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                rows={8}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.content ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Tulis konten berita lengkap di sini..."
                autoComplete="off"
              />
              {errors.content && <p className="text-red-600 text-sm mt-1">{errors.content}</p>}
            </div>

            {/* Status */}
            <div>
              <label htmlFor="news-status" className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
              id="news-status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>

                {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gambar Berita
              </label>
              <div className="space-y-4">
                {/* Image Preview */}
                {(imagePreview || (modalMode === 'edit' && formData.oldImage)) && (
                  <div className="relative">
                    <img
                      src={
                        imagePreview ||
                        (modalMode === 'edit' ? formData.oldImage : '') ||
                        '/images/placeholder.svg'
                      }
                      alt="Preview"
                      className="w-full h-48 object-cover rounded-lg border border-gray-300"
                      onError={e => {
                        e.target.onerror = null;
                        e.target.src = '/images/placeholder.svg';
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        if (modalMode === 'edit') {
                          // Reset both image preview and form data
                          setImagePreview(null);
                          setFormData(prev => ({ 
                            ...prev, 
                            image: null, 
                            oldImage: null,
                            image_url: null 
                          }));
                        } else {
                          setImagePreview(null);
                          setFormData(prev => ({ ...prev, image: null }));
                        }
                      }}
                      className="absolute top-2 right-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}                {/* File Input */}
                <div className="flex items-center justify-center w-full">
                  <label htmlFor="news-image-upload" className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <svg className="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">Klik untuk upload</span> atau drag and drop
                      </p>
                      <p className="text-xs text-gray-500">PNG, JPG, JPEG (MAX. 5MB)</p>
                    </div>
                    <input
                      id="news-image-upload"
                      name="image"
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </label>
                </div>

                {errors.image && <p className="text-red-600 text-sm">{errors.image}</p>}
              </div>
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={closeModal}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Batal
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                {modalMode === 'create' ? 'Simpan' : 'Update'}
              </button>
            </div>
          </form>
        </AdminModal>

        {/* View Modal */}
        <AdminModal
          isOpen={isModalOpen && modalMode === 'view'}
          onClose={closeModal}
          title="Detail Berita"
          size="xl"
        >
          {selectedNews && (
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{selectedNews.title}</h3>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span>Kategori: {typeof selectedNews.category === 'object' ? selectedNews.category?.name : selectedNews.category || 'Tidak ada kategori'}</span>
                  <span>Penulis: {typeof selectedNews.author === 'object' ? selectedNews.author?.name : selectedNews.author ||
                                  typeof selectedNews.author_name === 'object' ? selectedNews.author_name?.name : selectedNews.author_name ||
                                  'Admin'}</span>
                  <span>Tanggal: {selectedNews.published_at ? new Date(selectedNews.published_at).toLocaleDateString('id-ID') :
                                  selectedNews.created_at ? new Date(selectedNews.created_at).toLocaleDateString('id-ID') : '-'}</span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedNews.status || 'draft')}`}>
                    {selectedNews.status === 'published' ? 'Published' :
                     selectedNews.status === 'archived' ? 'Archived' : 'Draft'}
                  </span>
                </div>
              </div>

              {/* Image */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Gambar:</h4>
                {selectedNews && selectedNews.id ? (
                  <img
                    src={getImageUrl(selectedNews)}
                    alt={selectedNews.title}
                    className="w-full max-w-md h-48 object-cover rounded-lg border border-gray-300"
                    onError={e => {
                      e.target.onerror = null;
                      e.target.src = '/images/placeholder.svg';
                    }}
                  />
                ) : (
                  <img
                    src="/images/placeholder.svg"
                    alt="No Image"
                    className="w-full max-w-md h-48 object-cover rounded-lg border border-gray-300 opacity-60"
                  />
                )}
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Ringkasan:</h4>
                <p className="text-gray-700">{selectedNews.excerpt}</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Konten:</h4>
                <div className="text-gray-700 whitespace-pre-wrap">{selectedNews.content}</div>
              </div>
              
              <div className="flex justify-end pt-4">
                <button
                  onClick={closeModal}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Tutup
                </button>
              </div>
            </div>
          )}
        </AdminModal>


      </div>
    </AdminLayout>
    </ErrorBoundary>
  );
};

export default NewsManagement;
