import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import AdminLayout from '../../components/admin/AdminLayout';
import { Button, Card, Input, Textarea, FadeIn } from '../../components/ui';
import { api } from '../../services/api';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import AboutSettings from '../../components/admin/AboutSettings';
import AuthErrorComponent from '../../components/auth/AuthErrorComponent';
import LogoImage from '../../components/LogoImage';
import {
  validateLogoFile,
  createLogoPreview,
  getLogoUrl,
  updateLogoEverywhere,
  getLatestLogo,
  DEFAULT_LOGO_PATH,
  fixLogoUrl
} from '../../utils/logoUtils';

// Memory cache untuk settings data
const settingsMemoryCache = new Map();

// Cache untuk settings data
const getCachedSettingsData = () => {
  try {
    // Check memory cache first
    if (settingsMemoryCache.has('settingsData')) {
      return settingsMemoryCache.get('settingsData');
    }

    // Then check localStorage
    const cached = localStorage.getItem('settingsData');
    if (cached) {
      const parsedData = JSON.parse(cached);
      settingsMemoryCache.set('settingsData', parsedData);
      return parsedData;
    }
  } catch {
    // Silent error handling
  }

  // Return default settings object
  return {
    school_name: '',
    school_address: '',
    school_phone: '',
    school_email: '',
    school_website: '',
    school_description: '',
    primary_color: '#3B82F6',
    secondary_color: '#10B981',
    accent_color: '#F59E0B',
    logo_url: null
  };
};
// SVG Icons Components
const PaintBrushIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 5H3m4 6V9m0 0V7m0 2H9m-2 0H5" />
  </svg>
);

const EyeIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
  </svg>
);

const EyeSlashIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
  </svg>
);

const SwatchIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 5H3m4 6V9m0 0V7m0 2H9m-2 0H5" />
  </svg>
);

const GlobeIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
  </svg>
);

// Helper function to format file size
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

const MobileIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
  </svg>
);

const ComputerIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
);

const PlayIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M13 16h-1.586a1 1 0 01-.707-.293l-2.414-2.414a1 1 0 00-.707-.293H7m3-2v4" />
  </svg>
);

const ShareIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
  </svg>
);

const InstagramIcon = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.036.388a3.9 3.9 0 00-1.407.923A3.9 3.9 0 00.706 4.718C.522 5.202.4 5.776.366 6.723.331 7.671.318 8.078.318 11.699s.013 4.028.048 4.976c.034.947.156 1.521.34 2.005a3.9 3.9 0 00.923 1.407 3.9 3.9 0 001.407.923c.484.184 1.058.306 2.005.34.948.035 1.355.048 4.976.048s4.028-.013 4.976-.048c.947-.034 1.521-.156 2.005-.34a3.9 3.9 0 001.407-.923 3.9 3.9 0 00.923-1.407c.184-.484.306-1.058.34-2.005.035-.948.048-1.355.048-4.976s-.013-4.028-.048-4.976c-.034-.947-.156-1.521-.34-2.005a3.9 3.9 0 00-.923-1.407A3.9 3.9 0 0018.282.706c-.484-.184-1.058-.306-2.005-.34C15.329.013 14.922 0 11.301 0h.716zm-.132 5.925a6.074 6.074 0 110 12.148 6.074 6.074 0 010-12.148zm0 10.013a3.939 3.939 0 100-7.878 3.939 3.939 0 000 7.878zM18.406 4.155a1.44 1.44 0 11-2.88 0 1.44 1.44 0 012.88 0z"/>
  </svg>
);

const YoutubeIcon = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
);

const FacebookIcon = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

const TwitterIcon = ({ className }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
);

const SaveIcon = ({ className }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
  </svg>
);




// Validation schema
const settingsSchema = yup.object().shape({
  schoolName: yup
    .string()
    .required('Nama sekolah harus diisi')
    .min(5, 'Nama sekolah minimal 5 karakter')
    .max(100, 'Nama sekolah maksimal 100 karakter'),
  schoolShortName: yup
    .string()
    .required('Nama singkat sekolah harus diisi')
    .min(3, 'Nama singkat minimal 3 karakter')
    .max(20, 'Nama singkat maksimal 20 karakter'),
  schoolAddress: yup
    .string()
    .required('Alamat sekolah harus diisi')
    .min(10, 'Alamat minimal 10 karakter')
    .max(200, 'Alamat maksimal 200 karakter'),
  schoolPhone: yup
    .string()
    .required('Nomor telepon harus diisi')
    .matches(/^(\+62|62|0)[0-9]{8,13}$/, 'Format nomor telepon tidak valid'),
  schoolEmail: yup
    .string()
    .email('Format email tidak valid')
    .required('Email sekolah harus diisi'),
  schoolWebsite: yup
    .string()
    .url('Format website tidak valid')
    .required('Website sekolah harus diisi'),
  principalName: yup
    .string()
    .required('Nama kepala sekolah harus diisi')
    .min(3, 'Nama kepala sekolah minimal 3 karakter')
    .max(100, 'Nama kepala sekolah maksimal 100 karakter'),
  schoolMotto: yup
    .string()
    .required('Motto sekolah harus diisi')
    .min(5, 'Motto minimal 5 karakter')
    .max(200, 'Motto maksimal 200 karakter'),
  schoolDescription: yup
    .string()
    .required('Deskripsi sekolah harus diisi')
    .min(20, 'Deskripsi minimal 20 karakter')
    .max(1000, 'Deskripsi maksimal 1000 karakter'),
  websiteTitle: yup
    .string()
    .required('Judul website harus diisi')
    .min(5, 'Judul website minimal 5 karakter')
    .max(100, 'Judul website maksimal 100 karakter')
});

const SettingsManagement = () => {
  const { isAuthenticated, isLoading: authLoading, authError, clearAuthError, redirectToLogin } = useAdminAuth();
  // Initialize with cached data - always show content immediately
  const cachedSettings = getCachedSettingsData();
  const [settings, setSettings] = useState(cachedSettings);
  const [hasTriedFetch, setHasTriedFetch] = useState(false);

  const [selectedLogo, setSelectedLogo] = useState({
    file: null,
    type: '',
    size: 0,
    name: ''
  });
  const [logoPreview, setLogoPreview] = useState(null);

  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [logoInfo, setLogoInfo] = useState({
    url: '',
    filename: '',
    file_size: 0,
    formatted_size: '0 KB',
    file_exists: false
  });

  const [logoLoading, setLogoLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('school-info'); // school-info, about-settings, theme

  // Key Features state
  const [keyFeatures, setKeyFeatures] = useState([
    'Akreditasi A',
    'Fasilitas Lengkap',
    'Guru Berkualitas',
    'Teknologi Modern'
  ]);
  const [newFeature, setNewFeature] = useState('');
  const [editingFeature, setEditingFeature] = useState(null); // Track which feature is being edited

  // Key Features functions
  const handleAddFeature = () => {
    const trimmedFeature = newFeature.trim();
    if (trimmedFeature && !keyFeatures.includes(trimmedFeature)) {
      if (keyFeatures.length >= 8) {
        alert('Maksimal 8 fitur unggulan yang dapat ditambahkan');
        return;
      }
      const updatedFeatures = [...keyFeatures, trimmedFeature];
      setKeyFeatures(updatedFeatures);
      setSettings(prev => ({ ...prev, keyFeatures: updatedFeatures }));
      setNewFeature('');
    } else if (keyFeatures.includes(trimmedFeature)) {
      alert('Fitur ini sudah ada dalam daftar');
    }
  };

  const handleRemoveFeature = (index) => {
    const updatedFeatures = keyFeatures.filter((_, i) => i !== index);
    setKeyFeatures(updatedFeatures);
    setSettings(prev => ({ ...prev, keyFeatures: updatedFeatures }));
  };

  const handleEditFeature = (index, newValue) => {
    const updatedFeatures = [...keyFeatures];
    updatedFeatures[index] = newValue;
    setKeyFeatures(updatedFeatures);
    setSettings(prev => ({ ...prev, keyFeatures: updatedFeatures }));
  };

  const handleStartEdit = (index) => {
    setEditingFeature(index);
  };

  const handleFinishEdit = () => {
    setEditingFeature(null);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleFinishEdit();
    } else if (e.key === 'Escape') {
      handleFinishEdit();
    }
  };

  const handleMoveFeature = (index, direction) => {
    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= keyFeatures.length) return;

    const updatedFeatures = [...keyFeatures];
    [updatedFeatures[index], updatedFeatures[newIndex]] = [updatedFeatures[newIndex], updatedFeatures[index]];
    setKeyFeatures(updatedFeatures);
    setSettings(prev => ({ ...prev, keyFeatures: updatedFeatures }));
  };

  // Theme settings state
  const [themeSettings, setThemeSettings] = useState({
    // Top navbar colors
    topNavBg: '#1e40af',
    topNavText: '#ffffff',
    topNavIconColor: '#e5e7eb',

    // Main navbar colors
    mainNavBg: '#2563eb',
    mainNavText: '#ffffff',
    mainNavHover: '#3b82f6',

    // Social media URLs
    socialMedia: {
      instagram: 'https://instagram.com',
      youtube: 'https://youtube.com',
      facebook: 'https://facebook.com',
      twitter: 'https://twitter.com'
    }
  });

  const [themePreview, setThemePreview] = useState(false);
  const [themeSaving, setThemeSaving] = useState(false);

  // Handle theme save
  const handleThemeSave = async () => {
    try {
      setThemeSaving(true);
      console.log('Saving theme settings:', themeSettings);

      const response = await api.post('/admin/theme/update', themeSettings);

      if (response.data.success) {
        console.log('Theme saved successfully:', response.data);

        // Apply theme to current page
        applyThemeToPage(themeSettings);

        // Show success message
        alert('Theme berhasil disimpan! Halaman akan di-reload untuk menerapkan perubahan.');

        // Reload page to apply changes
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        throw new Error(response.data.message || 'Failed to save theme');
      }
    } catch (error) {
      console.error('Error saving theme:', error);
      alert('Gagal menyimpan theme: ' + error.message);
    } finally {
      setThemeSaving(false);
    }
  };

  // Apply theme to current page
  const applyThemeToPage = (theme) => {
    const root = document.documentElement;
    root.style.setProperty('--top-nav-bg', theme.topNavBg);
    root.style.setProperty('--top-nav-text', theme.topNavText);
    root.style.setProperty('--top-nav-icon-color', theme.topNavIconColor);
    root.style.setProperty('--main-nav-bg', theme.mainNavBg);
    root.style.setProperty('--main-nav-text', theme.mainNavText);
    root.style.setProperty('--main-nav-hover', theme.mainNavHover);
  };



  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset
  } = useForm({
    resolver: yupResolver(settingsSchema),
    defaultValues: {
      schoolName: '',
      schoolShortName: '',
      schoolAddress: '',
      schoolPhone: '',
      schoolEmail: '',
      schoolWebsite: '',
      principalName: '',
      schoolMotto: '',
      schoolDescription: '',
      websiteTitle: ''
    }
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setError(null);

        const response = await api.get('/settings');

        if (response.data.success) {
          const settingsData = response.data.data;

          const finalSettings = {
            ...settingsData,
            logoUrl: settingsData.logoUrl || getLogoUrl(),
            keyFeatures: settingsData.keyFeatures || [
              'Akreditasi A',
              'Fasilitas Lengkap',
              'Guru Berkualitas',
              'Teknologi Modern'
            ]
          };

          setSettings(finalSettings);
          setKeyFeatures(finalSettings.keyFeatures);
          setHasTriedFetch(true);

          // Save to cache
          settingsMemoryCache.set('settingsData', finalSettings);
          localStorage.setItem('settingsData', JSON.stringify(finalSettings));

          reset(finalSettings);
        } else {
          setError('Gagal memuat pengaturan');
          setHasTriedFetch(true);
        }
      } catch (err) {
        console.error('Error fetching settings:', err);
        setError('Terjadi kesalahan saat memuat pengaturan');
        setHasTriedFetch(true);
        // Keep cached data
      }
    };

    fetchSettings();
    loadThemeSettings();
  }, [reset]);

  // Reset form when settings change
  useEffect(() => {
    if (settings && Object.keys(settings).length > 0) {
      console.log('Resetting form with settings:', settings);
      reset(settings);
    }
  }, [settings, reset]);



  // Load theme settings
  const loadThemeSettings = async () => {
    try {
      const response = await api.get('/theme-settings');
      if (response.data.success) {
        const themeData = response.data.data;
        setThemeSettings({
          topNavBg: themeData.colors.topNavBg,
          topNavText: themeData.colors.topNavText,
          topNavIconColor: themeData.colors.topNavIconColor,
          mainNavBg: themeData.colors.mainNavBg,
          mainNavText: themeData.colors.mainNavText,
          mainNavHover: themeData.colors.mainNavHover,
          socialMedia: themeData.socialMedia
        });
        console.log('Theme settings loaded:', themeData);
      }
    } catch (error) {
      console.error('Error loading theme settings:', error);
    }
  };

  // Load latest logo info and all logos
  useEffect(() => {
    const loadLogoInfo = async () => {
      // Cek apakah logo sudah ada dan tidak perlu reload
      if (logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH && logoInfo.file_exists) {
        console.log('Logo sudah ada dan valid, skip reload:', logoInfo.url);
        return;
      }

      try {
        console.log('Loading latest logo from folder...');

        // Try to get latest logo from API first
        const latestLogo = await getLatestLogo();
        if (latestLogo && latestLogo.url) {
          console.log('Using latest logo from API:', latestLogo.url);

          setLogoInfo(latestLogo);
          setSettings(prev => ({ ...prev, logoUrl: latestLogo.url }));
          sessionStorage.setItem('cachedLogoUrl', latestLogo.url);
          setTimeout(() => updateLogoEverywhere(latestLogo.url, settings), 100);
          return; // Skip fallback API call if we have latest
        }

        console.log('Fallback to API call...');

        // Get current logo from API
        const response = await api.get('/logo/current');
        console.log('Logo API response:', response.data);

        if (response.data.success) {
          const logoData = response.data.data;
          console.log('Logo data received:', logoData);

          // Fix URL jika masih menggunakan storage path
          const logoUrl = fixLogoUrl(logoData.url);
          console.log('Fixed logo URL:', logoUrl);

          // Update logoInfo dengan URL yang sudah diperbaiki
          setLogoInfo({
            ...logoData,
            url: logoUrl
          });

          // Update settings dengan logo URL yang sudah diperbaiki
          setSettings(prev => ({
            ...prev,
            logoUrl: logoUrl
          }));

          // Cache logo URL yang sudah diperbaiki
          sessionStorage.setItem('cachedLogoUrl', logoUrl);

          // Update favicon dan navbar langsung saat load
          setTimeout(() => {
            updateLogoEverywhere(logoUrl, settings);
          }, 100);
        } else {
          console.log('No logo found, using default');
          throw new Error('No logo found');
        }
      } catch (error) {
        console.error('Error loading logo:', error);

        // Jika tidak ada logo, gunakan default
        const defaultUrl = 'http://localhost:8000/images/logo/logo-school.png';
        setSettings(prev => ({
          ...prev,
          logoUrl: defaultUrl
        }));
        setLogoInfo(null);
        sessionStorage.setItem('cachedLogoUrl', defaultUrl);
      } finally {
        setLogoLoading(false);
      }
    };

    loadLogoInfo();

    // Listen for logo updates - dengan debounce untuk mencegah refresh berulang
    let logoUpdateTimeout;
    const handleLogoUpdate = (event) => {
      console.log('Logo update event received:', event.detail);

      // Clear timeout sebelumnya untuk debounce
      if (logoUpdateTimeout) {
        clearTimeout(logoUpdateTimeout);
      }

      // Hanya reload jika benar-benar ada perubahan logo dan belum ada logo yang ditampilkan
      if (event.detail && event.detail.logoUrl && event.detail.logoUrl !== logoInfo?.url) {
        console.log('Logo berubah, reloading...');

        // Debounce untuk mencegah multiple calls
        logoUpdateTimeout = setTimeout(() => {
          // Hanya reload jika belum ada logo yang valid
          if (!logoInfo || !logoInfo.file_exists || logoInfo.url === DEFAULT_LOGO_PATH) {
            loadLogoInfo();
          }
        }, 300);
      } else {
        console.log('Logo tidak berubah atau sudah ada, skip reload');
      }
    };

    window.addEventListener('logoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('logoUpdated', handleLogoUpdate);
      // Clear timeout jika ada
      if (logoUpdateTimeout) {
        clearTimeout(logoUpdateTimeout);
      }
    };
  }, [logoInfo, settings]);

  const handleFormSubmit = async (data) => {
    setIsSaving(true);
    try {
      console.log('Form data received:', data);
      console.log('Current settings:', settings);

      // Ensure all required fields are present
      const requiredFields = [
        'schoolName',
        'schoolShortName',
        'schoolAddress',
        'schoolPhone',
        'schoolEmail',
        'schoolWebsite',
        'principalName',
        'schoolMotto',
        'schoolDescription',
        'websiteTitle'
      ];

      const missingFields = requiredFields.filter(field => !data[field] || data[field].trim() === '');
      if (missingFields.length > 0) {
        console.error('Missing fields:', missingFields);
        throw new Error(`Semua field harus diisi. Field yang kosong: ${missingFields.join(', ')}`);
      }

      const settingsData = {
        ...data,
        logoUrl: settings.logoUrl || 'http://localhost:8000/images/logo/logo-school.png',
        keyFeatures: keyFeatures
      };

      console.log('Sending settings data:', settingsData);
      const response = await api.put('/settings', settingsData);

      const result = response.data;

      if (result.success) {
        const updatedSettings = result.data;
        setSettings(updatedSettings);

        // Update localStorage as cache
        localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

        // Trigger custom event to update other components
        window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
          detail: updatedSettings
        }));

        // Show success message
        setError(null);
        setShowSuccessModal(true);
        setTimeout(() => setShowSuccessModal(false), 3000);
      } else {
        throw new Error(result.error || 'Failed to save settings');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Terjadi kesalahan saat menyimpan pengaturan: ' + err.message);
    } finally {
      setIsSaving(false);
    }
  };


  // Logo file handler
  const handleLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const validation = validateLogoFile(file);
      if (!validation.success) {
        setError(validation.message);
        return;
      }
      setSelectedLogo({
        file,
        type: file.type,
        size: file.size,
        name: file.name
      });
      try {
        const preview = await createLogoPreview(file);
        setLogoPreview(preview);
      } catch (error) {
        console.error('Error creating preview:', error);
        setError('Gagal membuat preview gambar');
      }
    }
  };



  const handleLogoSave = async () => {
    const hasLogoToUpload = selectedLogo && selectedLogo.file && logoPreview;

    if (!hasLogoToUpload) {
      console.log('No logo selected for upload');
      return;
    }

    setIsSaving(true);
    try {
      // Upload logo if available
      if (hasLogoToUpload) {
        console.log('Uploading logo to /logo/upload endpoint...');
        console.log('Selected logo file:', selectedLogo.file);

        const formData = new FormData();
        formData.append('logo', selectedLogo.file);
        formData.append('description', 'Logo sekolah diupload dari admin panel');

        console.log('FormData prepared, sending to API...');
        const response = await api.post('/logo/upload', formData);
        const result = response.data;
        console.log('Logo upload response:', result);
        if (result.success) {
          const logoData = result.data;
          console.log('Logo upload successful:', logoData);

          // Fix logo URL if needed
          const fixedLogoUrl = fixLogoUrl(logoData.url);
          console.log('Fixed logo URL:', fixedLogoUrl);

          const updatedSettings = {
            ...settings,
            logoUrl: fixedLogoUrl
          };
          setSettings(updatedSettings);
          localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

          // Update favicon, navbar logo, and page title everywhere
          updateLogoEverywhere(fixedLogoUrl, updatedSettings);

          // Update logo info state immediately
          setLogoInfo({
            url: fixedLogoUrl,
            filename: logoData.filename || 'logo.jpg',
            file_size: logoData.file_size || selectedLogo.file.size,
            formatted_size: logoData.formatted_size || formatBytes(selectedLogo.file.size),
            file_exists: true
          });

          // Clear preview after successful upload
          setLogoPreview(null);
          setSelectedLogo(null);

          // Reset file input
          const fileInput = document.getElementById('logo-upload');
          if (fileInput) {
            fileInput.value = '';
          }

          try {
            await api.put('/settings', {
              ...updatedSettings,
              logoUrl: fixedLogoUrl
            });
          } catch (settingsError) {
            console.warn('Failed to update settings:', settingsError);
          }

          // Force reload logo info to get latest
          setTimeout(async () => {
            try {
              const latestLogo = await getLatestLogo();
              if (latestLogo && latestLogo.url) {
                const fixedLatestUrl = fixLogoUrl(latestLogo.url);
                setLogoInfo({...latestLogo, url: fixedLatestUrl});
                setSettings(prev => ({ ...prev, logoUrl: fixedLatestUrl }));
                updateLogoEverywhere(fixedLatestUrl, updatedSettings);
              }
            } catch (error) {
              console.warn('Failed to reload latest logo:', error);
            }
          }, 500);

          window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
            detail: updatedSettings
          }));
          window.dispatchEvent(new CustomEvent('logoUpdated'));
          setSelectedLogo({ file: null, type: '', size: 0, name: '' });
          setLogoPreview(null);
          setLogoInfo({
            url: logoData.url,
            filename: logoData.filename,
            file_size: logoData.file_size,
            formatted_size: logoData.formatted_size,
            file_exists: true
          });
          // Auto redirect to settings page after successful upload
          setTimeout(() => {
            window.location.href = '/admin/settings';
          }, 1000);
        } else {
          throw new Error(result.message || 'Failed to upload logo');
        }
      }
    } catch (err) {
      console.error('❌ Error uploading logo:', err);
      // Silent error handling - just log to console
      // Auto redirect to settings page even on error
      setTimeout(() => {
        window.location.href = '/admin/settings';
      }, 1000);
    } finally {
      setIsSaving(false);
    }
  };



  // Test upload with sample file
  const handleTestUpload = async () => {
    try {
      // Create a simple test image (1x1 pixel PNG)
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#3B82F6';
      ctx.fillRect(0, 0, 1, 1);

      canvas.toBlob(async (blob) => {
        const testFile = new File([blob], 'test-logo.png', { type: 'image/png' });

        console.log('🧪 Testing upload with generated file...');

        const formData = new FormData();
        formData.append('logo', testFile);
        formData.append('description', 'Test upload from frontend');

        const response = await api.post('/logos/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data.success) {
          const logoData = response.data.data;
          console.log('✅ Test upload successful:', logoData);

          // Fix logo URL if needed
          const fixedLogoUrl = fixLogoUrl(logoData.url);

          // Update UI with test logo
          const updatedSettings = {
            ...settings,
            logoUrl: fixedLogoUrl
          };
          setSettings(updatedSettings);

          // Refresh logo info
          setLogoInfo({
            url: fixedLogoUrl,
            filename: logoData.filename,
            file_size: logoData.file_size,
            formatted_size: logoData.formatted_size,
            file_exists: true
          });

          // Test upload berhasil - tidak perlu alert
        }
      }, 'image/png');

    } catch (error) {
      console.error('❌ Test upload error:', error);
      setError(`Test upload gagal: ${error.message}`);
    }
  };

  const handleLogoReset = async () => {
    try {
      // For database version, we'll just set the default logo URL
      const defaultLogo = DEFAULT_LOGO_PATH;
      const updatedSettings = {
        ...settings,
        logoUrl: defaultLogo
      };

      setSettings(updatedSettings);
      localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

      // Clear logo info
      setLogoInfo(null);

      // Clear cache
      sessionStorage.removeItem('cachedLogoUrl');

      // Trigger events
      window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
        detail: updatedSettings
      }));

      window.dispatchEvent(new CustomEvent('logoUpdated'));

      // Logo berhasil direset - tidak perlu alert
    } catch (error) {
      console.error('Error resetting logo:', error);
      setError('Terjadi kesalahan saat mereset logo');
    }
  };

  // Show auth loading
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show auth error if not authenticated
  if (!isAuthenticated) {
    return <AuthErrorComponent authError={authError} clearAuthError={clearAuthError} redirectToLogin={redirectToLogin} />;
  }

  // Remove loading screen - always show content immediately

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            <h3 className="font-semibold">Error</h3>
            <p>{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Muat Ulang
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <FadeIn direction="down" className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Pengaturan Sekolah</h1>
          <p className="text-gray-600 mt-2">Kelola informasi dan pengaturan umum sekolah</p>
        </FadeIn>

        {/* Tab Navigation */}
        <FadeIn delay={0.1} className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('school-info')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'school-info'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Informasi Sekolah
              </button>
              <button
                onClick={() => setActiveTab('about-settings')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'about-settings'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Pengaturan About
              </button>
              <button
                onClick={() => setActiveTab('theme')}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === 'theme'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <PaintBrushIcon className="w-4 h-4" />
                <span>Theme</span>
              </button>
            </nav>
          </div>
        </FadeIn>

        {/* Tab Content */}
        {activeTab === 'school-info' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Logo Settings */}
          <FadeIn delay={0.2} className="lg:col-span-1">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Logo Sekolah</h2>
              
              <div className="text-center">
                <div className="mb-6">
                  {logoLoading ? (
                    // Enhanced loading state
                    <div className="w-32 h-32 mx-auto bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center shadow-inner">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-10 w-10 border-b-3 border-blue-600 mx-auto mb-3"></div>
                        <p className="text-sm text-gray-600 font-medium">Memuat logo...</p>
                        <p className="text-xs text-gray-400 mt-1">Mohon tunggu</p>
                      </div>
                    </div>
                  ) : (
                    // Always show preview - either logoPreview, settings.logoUrl, or fallback
                    <div className="relative">
                      <LogoImage
                        src={logoPreview || settings.logoUrl}
                        alt="Logo Sekolah"
                        className="w-32 h-32 mx-auto object-contain bg-gray-50 rounded-lg border-2 border-gray-200 shadow-sm transition-opacity duration-300"
                        style={{
                          minHeight: '128px',
                          minWidth: '128px',
                          opacity: logoLoading ? '0' : '1'
                        }}
                        showLoadingState={false}
                        loadingClassName="animate-pulse bg-gray-200 rounded-lg"
                        onLoad={(e) => {
                          console.log('✅ Settings logo loaded successfully:', e.target.src);
                          // Jangan trigger refresh jika logo sudah berhasil dimuat
                        }}
                        onError={(e) => {
                          console.log('❌ Settings logo error:', e.target.src);
                          // Hanya fallback ke default, jangan trigger refresh
                        }}
                      />
                      {logoPreview && (
                        <div className="absolute -top-2 -right-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Preview
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>



                <div className="space-y-4">
                  {/* Upload Button - Always centered */}
                  <div className="flex justify-center">
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp,image/*"
                      onChange={handleLogoChange}
                      className="hidden"
                    />
                    <Button
                      variant="primary"
                      size="sm"
                      className="cursor-pointer"
                      onClick={() => document.getElementById('logo-upload').click()}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      }
                    >
                      {logoPreview ? 'Ganti Logo' : 'Upload Logo Baru'}
                    </Button>
                  </div>

                  {/* Preview Action Buttons - Responsive layout */}
                  {logoPreview && (
                    <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-3">
                      <Button
                        variant="success"
                        size="sm"
                        onClick={handleLogoSave}
                        loading={isSaving}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Simpan Logo
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleTestUpload}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        }
                      >
                        Test Upload
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setLogoPreview(null);
                          setSelectedLogo(null);
                        }}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        }
                      >
                        Batal
                      </Button>
                    </div>
                  )}

                  {/* Main Action Buttons - Responsive layout */}
                  <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        // Cek apakah logo sudah ada dan tampil dengan benar
                        if (logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH && !logoLoading) {
                          console.log('Logo sudah tampil dengan benar, tidak perlu refresh:', logoInfo.url);
                          return; // Skip refresh jika logo sudah ada
                        }

                        setLogoLoading(true);
                        try {
                          const latestLogo = await getLatestLogo();
                          if (latestLogo && latestLogo.url) {
                            // Fix logo URL if needed
                            const fixedLatestUrl = fixLogoUrl(latestLogo.url);

                            // Cek apakah logo yang didapat sama dengan yang sudah ada
                            if (logoInfo && logoInfo.url === fixedLatestUrl) {
                              console.log('Logo sudah up-to-date, tidak perlu update');
                              return;
                            }

                            setLogoInfo({...latestLogo, url: fixedLatestUrl});
                            setSettings(prev => ({ ...prev, logoUrl: fixedLatestUrl }));
                            updateLogoEverywhere(fixedLatestUrl, settings);
                            console.log('Logo berhasil di-refresh:', fixedLatestUrl);
                          } else {
                            console.log('Tidak ada logo terbaru ditemukan');
                          }
                        } catch (error) {
                          console.error('Error refreshing logo:', error);
                        } finally {
                          setLogoLoading(false);
                        }
                      }}
                      disabled={logoLoading || (logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH && logoInfo.file_exists)}
                      className={logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH && logoInfo.file_exists ? 'opacity-50 cursor-not-allowed' : ''}
                      icon={
                        <svg className={`w-4 h-4 ${logoLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                      }
                      title={logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH ? 'Logo sudah tampil dengan benar' : 'Refresh logo dari server'}
                    >
                      {logoLoading ? 'Memuat...' :
                       (logoInfo && logoInfo.url && logoInfo.url !== DEFAULT_LOGO_PATH ? 'Logo OK' : 'Refresh Logo')}
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLogoReset}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      }
                    >
                      Reset ke Default
                    </Button>
                  </div>



                  {/* Format Information - Always at bottom */}
                  <div className="text-center pt-2 border-t border-gray-100">
                    <p className="text-xs text-gray-500 leading-relaxed">
                      <span className="font-medium text-gray-600">Format:</span> JPG, PNG, WebP<br />
                      <span className="font-medium text-gray-600">Ukuran maksimal:</span> 5MB<br />
                      <span className="font-medium text-gray-600">Rekomendasi:</span> 512x512px
                    </p>
                  </div>
                </div>


              </div>
            </Card>
          </FadeIn>

          {/* School Information Form */}
          <FadeIn delay={0.4} className="lg:col-span-2">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Informasi Sekolah</h2>
              
              <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nama Sekolah Lengkap"
                    placeholder="SMA Negeri 1 Jakarta"
                    autoComplete="organization"
                    required
                    error={formErrors.schoolName?.message}
                    {...register('schoolName')}
                  />

                  <Input
                    label="Nama Singkat"
                    placeholder="SMAN 1 Jakarta"
                    autoComplete="off"
                    required
                    error={formErrors.schoolShortName?.message}
                    {...register('schoolShortName')}
                  />
                </div>

                <Textarea
                  label="Alamat Sekolah"
                  placeholder="Jl. Pendidikan No. 123, Menteng, Jakarta Pusat"
                  rows={3}
                  autoComplete="street-address"
                  required
                  error={formErrors.schoolAddress?.message}
                  {...register('schoolAddress')}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nomor Telepon"
                    placeholder="021-12345678"
                    type="tel"
                    autoComplete="tel"
                    required
                    error={formErrors.schoolPhone?.message}
                    {...register('schoolPhone')}
                  />

                  <Input
                    label="Email Sekolah"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    required
                    error={formErrors.schoolEmail?.message}
                    {...register('schoolEmail')}
                  />
                </div>

                <Input
                  label="Website Sekolah"
                  type="url"
                  placeholder="https://www.sekolah.sch.id"
                  autoComplete="url"
                  required
                  error={formErrors.schoolWebsite?.message}
                  {...register('schoolWebsite')}
                />

                <Input
                  label="Nama Kepala Sekolah"
                  placeholder="Dr. Ahmad Suryadi, M.Pd"
                  autoComplete="name"
                  required
                  error={formErrors.principalName?.message}
                  {...register('principalName')}
                />

                <Input
                  label="Motto Sekolah"
                  placeholder="Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global"
                  autoComplete="off"
                  required
                  error={formErrors.schoolMotto?.message}
                  {...register('schoolMotto')}
                />

                <Textarea
                  label="Deskripsi Sekolah"
                  placeholder="Deskripsi singkat tentang sekolah..."
                  rows={4}
                  autoComplete="off"
                  required
                  error={formErrors.schoolDescription?.message}
                  {...register('schoolDescription')}
                />

                {/* Key Features Section */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700">
                    Fitur Unggulan Sekolah
                    <span className="text-red-500 ml-1">*</span>
                  </label>
                  <p className="text-sm text-gray-500">
                    Fitur-fitur unggulan yang akan ditampilkan di halaman profil sekolah.
                    <span className="font-medium text-blue-600">Klik pada teks untuk mengedit langsung.</span>
                  </p>

                  {/* Current Features */}
                  <div className="space-y-2">
                    {keyFeatures.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <p>Belum ada fitur unggulan</p>
                        <p className="text-sm">Tambahkan fitur unggulan pertama di bawah ini</p>
                      </div>
                    ) : (
                      keyFeatures.map((feature, index) => (
                      <div key={index} className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        {/* Feature Content */}
                        <div className="flex items-center gap-2 flex-1 w-full sm:w-auto">
                          <div className="w-3 h-3 bg-blue-600 rounded-full flex-shrink-0"></div>

                          {editingFeature === index ? (
                            // Edit mode - input field
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => handleEditFeature(index, e.target.value)}
                              onBlur={handleFinishEdit}
                              onKeyDown={(e) => handleKeyPress(e, index)}
                              className="flex-1 px-2 py-1 bg-white border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700 font-medium text-sm sm:text-base"
                              autoFocus
                            />
                          ) : (
                            // View mode - clickable text
                            <span
                              onClick={() => handleStartEdit(index)}
                              className="flex-1 text-gray-700 font-medium cursor-pointer hover:text-blue-600 transition-colors py-1 px-2 rounded hover:bg-white text-sm sm:text-base"
                              title="Klik untuk edit"
                            >
                              {feature}
                            </span>
                          )}
                        </div>

                        {/* Action Buttons - Responsive layout */}
                        <div className="flex items-center justify-end gap-1 w-full sm:w-auto">
                          {editingFeature === index ? (
                            // Edit mode buttons - Responsive
                            <div className="flex items-center gap-1">
                              <button
                                type="button"
                                onClick={handleFinishEdit}
                                className="text-green-600 hover:text-green-700 p-1.5 sm:p-1 rounded hover:bg-green-50 transition-colors"
                                title="Selesai edit"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              </button>
                              <button
                                type="button"
                                onClick={handleFinishEdit}
                                className="text-gray-500 hover:text-gray-700 p-1.5 sm:p-1 rounded hover:bg-gray-100 transition-colors"
                                title="Batal edit"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          ) : (
                            // View mode buttons - Responsive with better mobile layout
                            <div className="flex items-center gap-0.5 sm:gap-1">
                              {/* Move Up Button */}
                              <button
                                type="button"
                                onClick={() => handleMoveFeature(index, 'up')}
                                disabled={index === 0}
                                className={`p-1.5 sm:p-1 rounded transition-colors ${
                                  index === 0
                                    ? 'text-gray-300 cursor-not-allowed'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                                }`}
                                title="Pindah ke atas"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                                </svg>
                              </button>

                              {/* Move Down Button */}
                              <button
                                type="button"
                                onClick={() => handleMoveFeature(index, 'down')}
                                disabled={index === keyFeatures.length - 1}
                                className={`p-1.5 sm:p-1 rounded transition-colors ${
                                  index === keyFeatures.length - 1
                                    ? 'text-gray-300 cursor-not-allowed'
                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                                }`}
                                title="Pindah ke bawah"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                              </button>

                              {/* Edit Button */}
                              <button
                                type="button"
                                onClick={() => handleStartEdit(index)}
                                className="text-blue-500 hover:text-blue-700 p-1.5 sm:p-1 rounded hover:bg-blue-50 transition-colors"
                                title="Edit fitur"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>

                              {/* Delete Button */}
                              <button
                                type="button"
                                onClick={() => handleRemoveFeature(index)}
                                className="text-red-500 hover:text-red-700 p-1.5 sm:p-1 rounded hover:bg-red-50 transition-colors"
                                title="Hapus fitur"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      ))
                    )}
                  </div>

                  {/* Add New Feature - Responsive */}
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <input
                      type="text"
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder="Tambah fitur unggulan baru..."
                      className="flex-1 px-3 py-2.5 sm:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddFeature();
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddFeature}
                      disabled={!newFeature.trim() || keyFeatures.length >= 8}
                      className="w-full sm:w-auto justify-center"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      }
                    >
                      Tambah Fitur
                    </Button>
                  </div>

                  {/* Counter and Info - Responsive */}
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 text-sm text-gray-500 pt-2 border-t border-gray-100">
                    <span className="font-medium text-gray-600">
                      📊 {keyFeatures.length} dari 8 fitur maksimal
                    </span>
                    <span className="text-xs sm:text-sm text-gray-500">
                      💡 <span className="hidden sm:inline">Klik teks untuk edit, gunakan ↑↓ untuk mengatur urutan</span>
                      <span className="sm:hidden">Tap untuk edit, gunakan panah untuk urutan</span>
                    </span>
                  </div>
                </div>

                {/* Website Title */}
                <Input
                  label="Judul Website"
                  placeholder="Sistem Informasi SMA Negeri 1 Jakarta"
                  autoComplete="off"
                  required
                  error={formErrors.websiteTitle?.message}
                  {...register('websiteTitle')}
                  helperText="Judul yang akan tampil di tab browser"
                />

                <div className="flex justify-end pt-4">
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isSubmitting || isSaving}
                    disabled={isSubmitting || isSaving}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    }
                  >
                    Simpan Pengaturan
                  </Button>
                </div>
              </form>
            </Card>
          </FadeIn>
          </div>
        )}

        {/* About Settings Tab */}
        {activeTab === 'about-settings' && (
          <AboutSettings />
        )}

        {/* Theme Settings Tab */}
        {activeTab === 'theme' && (
          <div className="space-y-8">
            {/* Theme Preview */}
            <FadeIn delay={0.2}>
              <Card padding="lg">
                <div className="flex items-center space-x-3 mb-6">
                  <PaintBrushIcon className="w-6 h-6 text-blue-600" />
                  <h2 className="text-xl font-bold text-gray-900">Preview Theme</h2>
                </div>

                {/* Navbar Preview */}
                <div className="space-y-4">
                  {/* Top Navbar Preview */}
                  <div
                    className="p-4 rounded-lg shadow-sm"
                    style={{ backgroundColor: themeSettings.topNavBg }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div
                          className="flex items-center space-x-3"
                          style={{ color: themeSettings.topNavIconColor }}
                        >
                          <InstagramIcon className="w-4 h-4" />
                          <YoutubeIcon className="w-4 h-4" />
                          <FacebookIcon className="w-4 h-4" />
                          <TwitterIcon className="w-4 h-4" />
                        </div>
                      </div>
                      <div
                        className="text-sm"
                        style={{ color: themeSettings.topNavText }}
                      >
                        Jl. Pendidikan No. 123, Jakarta | 021-12345678
                      </div>
                    </div>
                  </div>

                  {/* Main Navbar Preview */}
                  <div
                    className="p-4 rounded-lg shadow-md"
                    style={{ backgroundColor: themeSettings.mainNavBg }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-white rounded mr-3"></div>
                        <span
                          className="text-xl font-bold"
                          style={{ color: themeSettings.mainNavText }}
                        >
                          {settings.schoolShortName || 'Nama Sekolah'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-6">
                        {['Beranda', 'Tentang', 'Berita', 'Galeri', 'Kontak'].map((item, index) => (
                          <span
                            key={index}
                            className="text-sm font-medium cursor-pointer hover:opacity-80 transition-opacity"
                            style={{ color: themeSettings.mainNavText }}
                          >
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex items-center space-x-4">
                  <Button
                    variant={themePreview ? "outline" : "primary"}
                    onClick={() => setThemePreview(!themePreview)}
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    {themePreview ? (
                      <>
                        <EyeSlashIcon className="w-4 h-4" />
                        <span>Matikan Preview</span>
                      </>
                    ) : (
                      <>
                        <EyeIcon className="w-4 h-4" />
                        <span>Aktifkan Preview</span>
                      </>
                    )}
                  </Button>
                  <span className="text-sm text-gray-500">
                    Preview akan diterapkan ke navbar asli
                  </span>
                </div>
              </Card>
            </FadeIn>

            {/* Color Settings */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Top Navbar Colors */}
              <FadeIn delay={0.3}>
                <Card padding="lg">
                  <div className="flex items-center space-x-2 mb-6">
                    <ComputerIcon className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Top Navbar</h3>
                  </div>

                  <div className="space-y-6">
                    {/* Background Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Background Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.topNavBg}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavBg: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.topNavBg}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavBg: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#1e40af"
                        />
                      </div>
                    </div>

                    {/* Text Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Text Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.topNavText}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavText: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.topNavText}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavText: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>

                    {/* Icon Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Icon Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.topNavIconColor}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavIconColor: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.topNavIconColor}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            topNavIconColor: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#e5e7eb"
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              </FadeIn>

              {/* Main Navbar Colors */}
              <FadeIn delay={0.4}>
                <Card padding="lg">
                  <div className="flex items-center space-x-2 mb-6">
                    <MobileIcon className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Main Navbar</h3>
                  </div>

                  <div className="space-y-6">
                    {/* Background Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Background Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.mainNavBg}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavBg: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.mainNavBg}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavBg: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#2563eb"
                        />
                      </div>
                    </div>

                    {/* Text Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Text Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.mainNavText}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavText: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.mainNavText}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavText: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#ffffff"
                        />
                      </div>
                    </div>

                    {/* Hover Color */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hover Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={themeSettings.mainNavHover}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavHover: e.target.value
                          }))}
                          className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={themeSettings.mainNavHover}
                          onChange={(e) => setThemeSettings(prev => ({
                            ...prev,
                            mainNavHover: e.target.value
                          }))}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="#3b82f6"
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              </FadeIn>
            </div>

            {/* Social Media Settings */}
            <FadeIn delay={0.5}>
              <Card padding="lg">
                <div className="flex items-center space-x-2 mb-6">
                  <GlobeIcon className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Social Media URLs</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Instagram */}
                  <div>
                    <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                      <InstagramIcon className="w-4 h-4 text-pink-500" />
                      <span>Instagram URL</span>
                    </label>
                    <input
                      type="url"
                      value={themeSettings.socialMedia.instagram}
                      onChange={(e) => setThemeSettings(prev => ({
                        ...prev,
                        socialMedia: {
                          ...prev.socialMedia,
                          instagram: e.target.value
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://instagram.com/username"
                    />
                  </div>

                  {/* YouTube */}
                  <div>
                    <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                      <YoutubeIcon className="w-4 h-4 text-red-500" />
                      <span>YouTube URL</span>
                    </label>
                    <input
                      type="url"
                      value={themeSettings.socialMedia.youtube}
                      onChange={(e) => setThemeSettings(prev => ({
                        ...prev,
                        socialMedia: {
                          ...prev.socialMedia,
                          youtube: e.target.value
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://youtube.com/channel/..."
                    />
                  </div>

                  {/* Facebook */}
                  <div>
                    <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                      <FacebookIcon className="w-4 h-4 text-blue-600" />
                      <span>Facebook URL</span>
                    </label>
                    <input
                      type="url"
                      value={themeSettings.socialMedia.facebook}
                      onChange={(e) => setThemeSettings(prev => ({
                        ...prev,
                        socialMedia: {
                          ...prev.socialMedia,
                          facebook: e.target.value
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://facebook.com/page"
                    />
                  </div>

                  {/* Twitter */}
                  <div>
                    <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                      <TwitterIcon className="w-4 h-4 text-sky-500" />
                      <span>Twitter URL</span>
                    </label>
                    <input
                      type="url"
                      value={themeSettings.socialMedia.twitter}
                      onChange={(e) => setThemeSettings(prev => ({
                        ...prev,
                        socialMedia: {
                          ...prev.socialMedia,
                          twitter: e.target.value
                        }
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="https://twitter.com/username"
                    />
                  </div>
                </div>
              </Card>
            </FadeIn>

            {/* Save Button */}
            <FadeIn delay={0.6}>
              <Card padding="lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center space-x-2">
                      <SwatchIcon className="w-5 h-5 text-green-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Simpan Theme</h3>
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Perubahan akan diterapkan ke seluruh website dan halaman akan di-reload otomatis
                    </p>
                  </div>
                  <Button
                    onClick={handleThemeSave}
                    loading={themeSaving}
                    disabled={themeSaving}
                    size="lg"
                    className="min-w-[120px]"
                  >
                    {themeSaving ? 'Menyimpan...' : 'Simpan Theme'}
                  </Button>
                </div>
              </Card>
            </FadeIn>
          </div>
        )}

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Berhasil!</h3>
              <p className="text-gray-600 mb-4">Logo berhasil diupload dan disimpan</p>
              <div className="text-sm text-gray-500">Halaman akan dimuat ulang...</div>
            </div>
          </div>
        )}

      </div>
    </AdminLayout>
  );
};

export default SettingsManagement;
