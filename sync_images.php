<?php

// Script untuk sinkronisasi gambar dan pembersihan cache
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== SYNC IMAGES & CLEAR CACHE ===\n\n";

// 1. Sync gambar news dari backend ke frontend
echo "📁 Syncing news images...\n";
$backendNewsPath = 'backend-laravel/public/images/news/';
$frontendNewsPath = 'frontend/public/images/news/';

if (is_dir($backendNewsPath)) {
    $newsFiles = glob($backendNewsPath . '*');
    $synced = 0;
    
    foreach ($newsFiles as $file) {
        $filename = basename($file);
        $targetFile = $frontendNewsPath . $filename;
        
        if (copy($file, $targetFile)) {
            $synced++;
        }
    }
    
    echo "✅ Synced $synced news images\n";
} else {
    echo "❌ Backend news directory not found\n";
}

// 2. Sync gambar gallery dari backend ke frontend
echo "📁 Syncing gallery images...\n";
$backendGalleryPath = 'backend-laravel/public/images/gallery/';
$frontendGalleryPath = 'frontend/public/images/gallery/';

if (is_dir($backendGalleryPath)) {
    $galleryFiles = glob($backendGalleryPath . '*');
    $synced = 0;
    
    foreach ($galleryFiles as $file) {
        $filename = basename($file);
        $targetFile = $frontendGalleryPath . $filename;
        
        if (copy($file, $targetFile)) {
            $synced++;
        }
    }
    
    echo "✅ Synced $synced gallery images\n";
} else {
    echo "❌ Backend gallery directory not found\n";
}

// 3. Clear browser cache dengan menambahkan cache busting
echo "🧹 Clearing cache...\n";

// Clear localStorage cache (akan dilakukan di browser)
$cacheScript = "
<script>
// Clear all cache
localStorage.clear();
sessionStorage.clear();

// Clear specific cache keys
const cacheKeys = [
    'schoolSettings',
    'themeSettings', 
    'socialMedia',
    'carouselImages'
];

cacheKeys.forEach(key => {
    localStorage.removeItem(key);
});

// Clear news cache
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.startsWith('news_') || key.startsWith('newsPage_') || key.startsWith('gallery_'))) {
        localStorage.removeItem(key);
        i--; // Adjust index after removal
    }
}

console.log('✅ Cache cleared successfully');
alert('Cache cleared! Please refresh the page.');
</script>
";

// Buat file HTML untuk clear cache
file_put_contents('clear_cache.html', "
<!DOCTYPE html>
<html>
<head>
    <title>Clear Cache</title>
</head>
<body>
    <h1>Clearing Cache...</h1>
    <p>Cache will be cleared automatically.</p>
    $cacheScript
</body>
</html>
");

echo "✅ Created clear_cache.html\n";

// 4. Validasi gambar yang hilang di database
echo "🔍 Checking for missing images in database...\n";

// Cek apakah ada file yang direferensikan tapi tidak ada
$missingImages = [];

// Cek news images
if (file_exists('backend-laravel/vendor/autoload.php')) {
    require_once 'backend-laravel/vendor/autoload.php';
    
    try {
        $app = require_once 'backend-laravel/bootstrap/app.php';
        $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
        
        // Check news images
        $newsItems = App\Models\News::whereNotNull('image_url')->get(['id', 'title', 'image_url']);
        
        foreach ($newsItems as $news) {
            if ($news->image_url) {
                // Extract filename from URL
                $filename = basename($news->image_url);
                $backendFile = $backendNewsPath . $filename;
                $frontendFile = $frontendNewsPath . $filename;
                
                if (!file_exists($backendFile)) {
                    $missingImages[] = [
                        'type' => 'news',
                        'id' => $news->id,
                        'title' => $news->title,
                        'filename' => $filename,
                        'url' => $news->image_url
                    ];
                }
            }
        }
        
        // Check gallery images
        $galleryItems = App\Models\Gallery::whereNotNull('image_url')->get(['id', 'title', 'image_url']);
        
        foreach ($galleryItems as $gallery) {
            if ($gallery->image_url) {
                // Extract filename from URL
                $filename = basename($gallery->image_url);
                $backendFile = $backendGalleryPath . $filename;
                $frontendFile = $frontendGalleryPath . $filename;
                
                if (!file_exists($backendFile)) {
                    $missingImages[] = [
                        'type' => 'gallery',
                        'id' => $gallery->id,
                        'title' => $gallery->title,
                        'filename' => $filename,
                        'url' => $gallery->image_url
                    ];
                }
            }
        }
        
        if (empty($missingImages)) {
            echo "✅ No missing images found\n";
        } else {
            echo "⚠️  Found " . count($missingImages) . " missing images:\n";
            foreach ($missingImages as $missing) {
                echo "   - {$missing['type']} ID {$missing['id']}: {$missing['filename']}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error checking database: " . $e->getMessage() . "\n";
    }
}

echo "\n=== SYNC COMPLETED ===\n";
echo "📋 Summary:\n";
echo "   - Images synced to frontend\n";
echo "   - Cache clearing script created\n";
echo "   - Missing images identified\n";
echo "\n💡 Next steps:\n";
echo "   1. Open clear_cache.html in browser to clear cache\n";
echo "   2. Refresh your application\n";
echo "   3. Check if images load correctly\n";

?>
