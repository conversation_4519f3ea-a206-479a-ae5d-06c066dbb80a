<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Gallery CRUD</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Test Gallery CRUD & Image Display</h1>
        
        <!-- Test Buttons -->
        <div class="mb-8 flex gap-4 justify-center flex-wrap">
            <button onclick="fetchGallery()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Load Gallery
            </button>
            <button onclick="fetchCarousel()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Load Carousel
            </button>
            <button onclick="testImageUrls()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                Test Image URLs
            </button>
        </div>

        <!-- Status Display -->
        <div id="status" class="mb-4 p-4 bg-white rounded shadow"></div>

        <!-- Gallery Grid -->
        <div id="galleryGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        
        // Function to get correct image URL (same as frontend)
        function getGalleryImageUrl(galleryItem) {
            if (!galleryItem || !galleryItem.image_url) {
                return '/images/school-building.jpg';
            }

            const imageUrl = galleryItem.image_url;
            console.log('Processing gallery image URL:', imageUrl, 'for item:', galleryItem.title);

            // If it's already a full URL from backend, convert to frontend path
            if (imageUrl.startsWith('http://localhost:8000/images/gallery/')) {
                const filename = imageUrl.split('/').pop();
                const frontendPath = `/images/gallery/${filename}`;
                console.log('Converting backend URL to frontend path:', frontendPath);
                return frontendPath;
            }

            // If it's already a full URL (starts with http), return as is
            if (imageUrl.startsWith('http')) {
                console.log('Using full URL:', imageUrl);
                return imageUrl;
            }

            // If it's /images/gallery path (frontend public format), use directly
            if (imageUrl.startsWith('/images/gallery/')) {
                console.log('Using frontend public gallery path:', imageUrl);
                return imageUrl;
            }

            // Default fallback
            console.log('Using fallback for:', imageUrl);
            return '/images/school-building.jpg';
        }

        async function fetchGallery() {
            try {
                document.getElementById('status').innerHTML = '<p class="text-blue-600">Loading gallery...</p>';
                
                const response = await fetch(`${API_BASE_URL}/gallery`);
                const data = await response.json();
                
                if (data.success) {
                    displayGallery(data.data, 'Gallery Items');
                    document.getElementById('status').innerHTML = 
                        `<p class="text-green-600">✅ Successfully loaded ${data.data.length} gallery items</p>`;
                } else {
                    throw new Error('Failed to fetch gallery');
                }
            } catch (error) {
                console.error('Error fetching gallery:', error);
                document.getElementById('status').innerHTML = 
                    `<p class="text-red-600">❌ Error: ${error.message}</p>`;
            }
        }

        async function fetchCarousel() {
            try {
                document.getElementById('status').innerHTML = '<p class="text-blue-600">Loading carousel...</p>';
                
                const response = await fetch(`${API_BASE_URL}/gallery/carousel`);
                const data = await response.json();
                
                if (data.success) {
                    displayGallery(data.data, 'Carousel Images');
                    document.getElementById('status').innerHTML = 
                        `<p class="text-green-600">✅ Successfully loaded ${data.data.length} carousel images</p>`;
                } else {
                    throw new Error('Failed to fetch carousel');
                }
            } catch (error) {
                console.error('Error fetching carousel:', error);
                document.getElementById('status').innerHTML = 
                    `<p class="text-red-600">❌ Error: ${error.message}</p>`;
            }
        }

        function displayGallery(galleryArray, title) {
            const grid = document.getElementById('galleryGrid');
            grid.innerHTML = `<div class="col-span-full mb-4"><h2 class="text-2xl font-bold text-gray-800">${title}</h2></div>`;

            galleryArray.forEach(item => {
                const imageUrl = getGalleryImageUrl(item);
                
                const card = document.createElement('div');
                card.className = 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow';
                
                card.innerHTML = `
                    <div class="aspect-[3/2] overflow-hidden bg-gray-200">
                        <img 
                            src="${imageUrl}" 
                            alt="${item.title}"
                            class="w-full h-full object-cover"
                            onerror="handleImageError(this, '${item.title}', '${item.image_url}')"
                            onload="handleImageLoad(this, '${item.title}', '${imageUrl}')"
                        />
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
                            ${item.title}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                            ${item.description || 'No description available'}
                        </p>
                        <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                            <span>ID: ${item.id}</span>
                            <span>${item.category || 'No category'}</span>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mb-2">
                            <span>Featured: ${item.featured ? 'Yes' : 'No'}</span>
                            <span>Carousel: ${item.carousel_pinned ? 'Yes' : 'No'}</span>
                        </div>
                        <div class="mt-2 text-xs text-gray-400">
                            <div>Original URL: ${item.image_url || 'No image URL'}</div>
                            <div>Processed URL: ${imageUrl}</div>
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        function handleImageError(img, title, originalUrl) {
            console.error('❌ Image load error for:', title, 'Original URL:', originalUrl, 'Attempted URL:', img.src);
            img.onerror = null;
            img.src = '/images/school-building.jpg';
        }

        function handleImageLoad(img, title, processedUrl) {
            console.log('✅ Image loaded successfully for:', title, 'URL:', processedUrl);
        }

        async function testImageUrls() {
            try {
                const response = await fetch(`${API_BASE_URL}/gallery`);
                const data = await response.json();
                
                if (data.success) {
                    console.log('=== GALLERY IMAGE URL TEST RESULTS ===');
                    data.data.forEach(item => {
                        const originalUrl = item.image_url;
                        const processedUrl = getGalleryImageUrl(item);
                        console.log(`Gallery ID ${item.id}: "${item.title}"`);
                        console.log(`  Original: ${originalUrl}`);
                        console.log(`  Processed: ${processedUrl}`);
                        console.log(`  Category: ${item.category}`);
                        console.log(`  Featured: ${item.featured ? 'Yes' : 'No'}`);
                        console.log(`  Carousel: ${item.carousel_pinned ? 'Yes' : 'No'}`);
                        console.log('---');
                    });
                    
                    document.getElementById('status').innerHTML = 
                        '<p class="text-blue-600">📋 Gallery URL test results logged to console</p>';
                }
            } catch (error) {
                console.error('Error testing gallery URLs:', error);
            }
        }

        // Auto-load gallery on page load
        window.addEventListener('load', fetchGallery);
    </script>
</body>
</html>
