<?php

// Test script untuk upload gallery dengan gambar
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TEST GALLERY UPLOAD WITH IMAGE ===\n\n";

// URL API
$apiUrl = 'http://localhost:8000/api/gallery';

// Data gallery
$galleryData = [
    'title' => 'Test Gallery dengan Gambar ' . date('Y-m-d H:i:s'),
    'description' => 'Ini adalah deskripsi test gallery yang dibuat melalui script PHP untuk menguji upload gambar.',
    'category' => 'Kegiatan Rutin',
    'featured' => '1',
    'carousel_pinned' => '1',
    'is_active' => '1',
    'sort_order' => '0'
];

// Buat gambar test sederhana
$testImagePath = __DIR__ . '/test_gallery_image.jpg';
if (!file_exists($testImagePath)) {
    // Buat gambar test 600x400 dengan teks
    $image = imagecreate(600, 400);
    $bgColor = imagecolorallocate($image, 34, 197, 94); // Green
    $textColor = imagecolorallocate($image, 255, 255, 255); // White
    
    imagefill($image, 0, 0, $bgColor);
    
    $text = "TEST GALLERY IMAGE\n" . date('Y-m-d H:i:s');
    imagestring($image, 5, 180, 180, $text, $textColor);
    
    imagejpeg($image, $testImagePath, 90);
    imagedestroy($image);
    
    echo "✅ Created test image: $testImagePath\n";
}

// Prepare multipart form data
$boundary = uniqid();
$postData = '';

// Add text fields
foreach ($galleryData as $key => $value) {
    $postData .= "--$boundary\r\n";
    $postData .= "Content-Disposition: form-data; name=\"$key\"\r\n\r\n";
    $postData .= "$value\r\n";
}

// Add image file
$postData .= "--$boundary\r\n";
$postData .= "Content-Disposition: form-data; name=\"image\"; filename=\"test_gallery_image.jpg\"\r\n";
$postData .= "Content-Type: image/jpeg\r\n\r\n";
$postData .= file_get_contents($testImagePath) . "\r\n";
$postData .= "--$boundary--\r\n";

// Setup cURL
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        "Content-Type: multipart/form-data; boundary=$boundary",
        "Accept: application/json"
    ],
    CURLOPT_TIMEOUT => 30
]);

echo "📤 Uploading gallery with image...\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

if ($error) {
    echo "❌ cURL Error: $error\n";
    exit(1);
}

echo "📥 HTTP Response Code: $httpCode\n";
echo "📄 Response Body:\n";
echo $response . "\n\n";

$responseData = json_decode($response, true);

if ($responseData && isset($responseData['success']) && $responseData['success']) {
    echo "✅ SUCCESS! Gallery created successfully\n";
    
    if (isset($responseData['data'])) {
        $gallery = $responseData['data'];
        echo "📋 Gallery Details:\n";
        echo "   ID: " . ($gallery['id'] ?? 'N/A') . "\n";
        echo "   Title: " . ($gallery['title'] ?? 'N/A') . "\n";
        echo "   Category: " . ($gallery['category'] ?? 'N/A') . "\n";
        echo "   Featured: " . (($gallery['featured'] ?? false) ? 'Yes' : 'No') . "\n";
        echo "   Carousel Pinned: " . (($gallery['carousel_pinned'] ?? false) ? 'Yes' : 'No') . "\n";
        echo "   Image URL: " . ($gallery['image_url'] ?? 'N/A') . "\n";
        
        // Test if image is accessible
        if (isset($gallery['image_url'])) {
            $imageUrl = $gallery['image_url'];
            echo "\n🔍 Testing image accessibility...\n";
            
            // Test backend URL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $imageUrl);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            curl_exec($ch);
            $backendHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($backendHttpCode == 200) {
                echo "✅ Backend image accessible: $imageUrl\n";
            } else {
                echo "❌ Backend image not accessible (HTTP $backendHttpCode): $imageUrl\n";
            }
            
            // Test frontend path
            if (strpos($imageUrl, 'http://localhost:8000/images/gallery/') === 0) {
                $filename = basename($imageUrl);
                $frontendPath = "http://localhost:5173/images/gallery/$filename";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $frontendPath);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                curl_exec($ch);
                $frontendHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($frontendHttpCode == 200) {
                    echo "✅ Frontend image accessible: $frontendPath\n";
                } else {
                    echo "❌ Frontend image not accessible (HTTP $frontendHttpCode): $frontendPath\n";
                }
            }
        }
    }
} else {
    echo "❌ FAILED! Error creating gallery\n";
    if (isset($responseData['message'])) {
        echo "Error message: " . $responseData['message'] . "\n";
    }
    if (isset($responseData['details'])) {
        echo "Validation errors:\n";
        foreach ($responseData['details'] as $field => $errors) {
            if (is_array($errors)) {
                echo "  $field: " . implode(', ', $errors) . "\n";
            } else {
                echo "  $field: $errors\n";
            }
        }
    }
}

// Test fetching all gallery items
echo "\n🔍 Testing gallery fetch...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ Gallery fetch successful. Total items: " . count($data['data']) . "\n";
        foreach ($data['data'] as $item) {
            echo "   - ID: {$item['id']} | {$item['title']} | {$item['category']}\n";
        }
    }
} else {
    echo "❌ Gallery fetch failed (HTTP $httpCode)\n";
}

// Cleanup
if (file_exists($testImagePath)) {
    unlink($testImagePath);
    echo "\n🧹 Cleaned up test image\n";
}

echo "\n=== TEST COMPLETED ===\n";
?>
