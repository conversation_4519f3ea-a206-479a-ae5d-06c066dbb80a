<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test News CRUD</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Test News CRUD & Image Display</h1>
        
        <!-- Test Buttons -->
        <div class="mb-8 flex gap-4 justify-center">
            <button onclick="fetchNews()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Load News
            </button>
            <button onclick="testImageUrls()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Test Image URLs
            </button>
        </div>

        <!-- Status Display -->
        <div id="status" class="mb-4 p-4 bg-white rounded shadow"></div>

        <!-- News Grid -->
        <div id="newsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        
        // Function to get correct image URL (same as frontend)
        function getImageUrl(newsItem) {
            if (!newsItem || !newsItem.image_url) {
                return '/images/placeholder.svg';
            }

            const imageUrl = newsItem.image_url;
            console.log('Processing news image URL:', imageUrl, 'for item:', newsItem.title);

            // If it's already a full URL from backend, convert to frontend path
            if (imageUrl.startsWith('http://localhost:8000/images/news/')) {
                const filename = imageUrl.split('/').pop();
                const frontendPath = `/images/news/${filename}`;
                console.log('Converting backend URL to frontend path:', frontendPath);
                return frontendPath;
            }

            // If it's already a full URL (starts with http), return as is
            if (imageUrl.startsWith('http')) {
                console.log('Using full URL:', imageUrl);
                return imageUrl;
            }

            // If it's /images/news path (frontend public format), use directly
            if (imageUrl.startsWith('/images/news/')) {
                console.log('Using frontend public news path:', imageUrl);
                return imageUrl;
            }

            // Default fallback
            console.log('Using fallback for:', imageUrl);
            return '/images/placeholder.svg';
        }

        async function fetchNews() {
            try {
                document.getElementById('status').innerHTML = '<p class="text-blue-600">Loading news...</p>';
                
                const response = await fetch(`${API_BASE_URL}/news`);
                const data = await response.json();
                
                if (data.success) {
                    displayNews(data.data);
                    document.getElementById('status').innerHTML = 
                        `<p class="text-green-600">✅ Successfully loaded ${data.data.length} news items</p>`;
                } else {
                    throw new Error('Failed to fetch news');
                }
            } catch (error) {
                console.error('Error fetching news:', error);
                document.getElementById('status').innerHTML = 
                    `<p class="text-red-600">❌ Error: ${error.message}</p>`;
            }
        }

        function displayNews(newsArray) {
            const grid = document.getElementById('newsGrid');
            grid.innerHTML = '';

            newsArray.forEach(news => {
                const imageUrl = getImageUrl(news);
                
                const card = document.createElement('div');
                card.className = 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow';
                
                card.innerHTML = `
                    <div class="aspect-[3/2] overflow-hidden bg-gray-200">
                        <img 
                            src="${imageUrl}" 
                            alt="${news.title}"
                            class="w-full h-full object-cover"
                            onerror="handleImageError(this, '${news.title}', '${news.image_url}')"
                            onload="handleImageLoad(this, '${news.title}', '${imageUrl}')"
                        />
                    </div>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
                            ${news.title}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                            ${news.excerpt || 'No excerpt available'}
                        </p>
                        <div class="flex justify-between items-center text-xs text-gray-500">
                            <span>ID: ${news.id}</span>
                            <span>${news.category || 'No category'}</span>
                        </div>
                        <div class="mt-2 text-xs text-gray-400">
                            <div>Original URL: ${news.image_url || 'No image URL'}</div>
                            <div>Processed URL: ${imageUrl}</div>
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        function handleImageError(img, title, originalUrl) {
            console.error('❌ Image load error for:', title, 'Original URL:', originalUrl, 'Attempted URL:', img.src);
            img.onerror = null;
            img.src = '/images/placeholder.svg';
        }

        function handleImageLoad(img, title, processedUrl) {
            console.log('✅ Image loaded successfully for:', title, 'URL:', processedUrl);
        }

        async function testImageUrls() {
            try {
                const response = await fetch(`${API_BASE_URL}/news`);
                const data = await response.json();
                
                if (data.success) {
                    console.log('=== IMAGE URL TEST RESULTS ===');
                    data.data.forEach(news => {
                        const originalUrl = news.image_url;
                        const processedUrl = getImageUrl(news);
                        console.log(`News ID ${news.id}: "${news.title}"`);
                        console.log(`  Original: ${originalUrl}`);
                        console.log(`  Processed: ${processedUrl}`);
                        console.log('---');
                    });
                    
                    document.getElementById('status').innerHTML = 
                        '<p class="text-blue-600">📋 Image URL test results logged to console</p>';
                }
            } catch (error) {
                console.error('Error testing image URLs:', error);
            }
        }

        // Auto-load news on page load
        window.addEventListener('load', fetchNews);
    </script>
</body>
</html>
