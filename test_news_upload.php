<?php

// Test script untuk upload berita dengan gambar
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== TEST NEWS UPLOAD WITH IMAGE ===\n\n";

// URL API
$apiUrl = 'http://localhost:8000/api/news';

// Data berita
$newsData = [
    'title' => 'Test Berita dengan Gambar ' . date('Y-m-d H:i:s'),
    'content' => 'Ini adalah konten test berita yang dibuat melalui script PHP untuk menguji upload gambar.',
    'excerpt' => 'Test berita dengan gambar untuk menguji CRUD functionality',
    'category' => 'Akademik',
    'author' => 'Admin Test',
    'status' => 'published',
    'featured' => '0'
];

// Buat gambar test sederhana
$testImagePath = __DIR__ . '/test_image.jpg';
if (!file_exists($testImagePath)) {
    // Buat gambar test 400x300 dengan teks
    $image = imagecreate(400, 300);
    $bgColor = imagecolorallocate($image, 135, 206, 235); // Sky blue
    $textColor = imagecolorallocate($image, 255, 255, 255); // White
    
    imagefill($image, 0, 0, $bgColor);
    
    $text = "TEST IMAGE\n" . date('Y-m-d H:i:s');
    imagestring($image, 5, 120, 130, $text, $textColor);
    
    imagejpeg($image, $testImagePath, 90);
    imagedestroy($image);
    
    echo "✅ Created test image: $testImagePath\n";
}

// Prepare multipart form data
$boundary = uniqid();
$postData = '';

// Add text fields
foreach ($newsData as $key => $value) {
    $postData .= "--$boundary\r\n";
    $postData .= "Content-Disposition: form-data; name=\"$key\"\r\n\r\n";
    $postData .= "$value\r\n";
}

// Add image file
$postData .= "--$boundary\r\n";
$postData .= "Content-Disposition: form-data; name=\"image\"; filename=\"test_image.jpg\"\r\n";
$postData .= "Content-Type: image/jpeg\r\n\r\n";
$postData .= file_get_contents($testImagePath) . "\r\n";
$postData .= "--$boundary--\r\n";

// Setup cURL
$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        "Content-Type: multipart/form-data; boundary=$boundary",
        "Accept: application/json"
    ],
    CURLOPT_TIMEOUT => 30
]);

echo "📤 Uploading news with image...\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

if ($error) {
    echo "❌ cURL Error: $error\n";
    exit(1);
}

echo "📥 HTTP Response Code: $httpCode\n";
echo "📄 Response Body:\n";
echo $response . "\n\n";

$responseData = json_decode($response, true);

if ($responseData && isset($responseData['success']) && $responseData['success']) {
    echo "✅ SUCCESS! News created successfully\n";
    
    if (isset($responseData['data'])) {
        $news = $responseData['data'];
        echo "📋 News Details:\n";
        echo "   ID: " . ($news['id'] ?? 'N/A') . "\n";
        echo "   Title: " . ($news['title'] ?? 'N/A') . "\n";
        echo "   Image URL: " . ($news['image_url'] ?? 'N/A') . "\n";
        
        // Test if image is accessible
        if (isset($news['image_url'])) {
            $imageUrl = $news['image_url'];
            echo "\n🔍 Testing image accessibility...\n";
            
            // Test backend URL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $imageUrl);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            curl_exec($ch);
            $backendHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($backendHttpCode == 200) {
                echo "✅ Backend image accessible: $imageUrl\n";
            } else {
                echo "❌ Backend image not accessible (HTTP $backendHttpCode): $imageUrl\n";
            }
            
            // Test frontend path
            if (strpos($imageUrl, 'http://localhost:8000/images/news/') === 0) {
                $filename = basename($imageUrl);
                $frontendPath = "http://localhost:5173/images/news/$filename";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $frontendPath);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                curl_exec($ch);
                $frontendHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($frontendHttpCode == 200) {
                    echo "✅ Frontend image accessible: $frontendPath\n";
                } else {
                    echo "❌ Frontend image not accessible (HTTP $frontendHttpCode): $frontendPath\n";
                }
            }
        }
    }
} else {
    echo "❌ FAILED! Error creating news\n";
    if (isset($responseData['message'])) {
        echo "Error message: " . $responseData['message'] . "\n";
    }
    if (isset($responseData['errors'])) {
        echo "Validation errors:\n";
        foreach ($responseData['errors'] as $field => $errors) {
            echo "  $field: " . implode(', ', $errors) . "\n";
        }
    }
}

// Cleanup
if (file_exists($testImagePath)) {
    unlink($testImagePath);
    echo "\n🧹 Cleaned up test image\n";
}

echo "\n=== TEST COMPLETED ===\n";
?>
